{"name": "bitlayer-website-v2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "remix vite:build", "deploy": "npm run build && wrangler pages deploy ./build/client", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "wrangler pages dev ./build/client", "typecheck": "tsc", "typegen": "wrangler types", "preview": "npm run build && wrangler pages dev ./build/client", "build-cf-types": "wrangler types", "test": "vitest", "test:ui": "vitest --ui", "upload": "crowdin upload sources", "download": "crowdin download -l", "download-all": "crowdin download --all", "download-approved": "crowdin download --export-only-approved", "postinstall": "patch-package"}, "browserslist": ["last 2 versions, not dead, > 0.2%"], "dependencies": {"@binance/w3w-wagmi-connector-v2": "^1.2.4-alpha.0", "@chainlink/ccip-js": "^0.2.2", "@crowdin/cli": "^4.1.0", "@fingerprintjs/fingerprintjs": "^4.3.0", "@gsap/react": "^2.1.0", "@hookform/resolvers": "^3.3.4", "@marsidev/react-turnstile": "^0.5.3", "@motionone/utils": "^10.17.0", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.0.7", "@react-hook/change": "^1.0.0", "@react-hook/copy": "^2.0.1", "@react-hook/debounce": "^4.0.0", "@react-hook/intersection-observer": "^3.1.1", "@react-hook/latest": "^1.0.3", "@react-hook/media-query": "^1.1.1", "@react-hook/window-size": "^3.1.1", "@remix-run/cloudflare": "^2.8.0", "@remix-run/cloudflare-pages": "^2.8.0", "@remix-run/node": "^2.8.0", "@remix-run/react": "^2.8.0", "@remix-run/serve": "^2.8.0", "@sadoprotocol/ordit-sdk": "^2.3.15", "@sentry/cloudflare": "^8.48.0", "@sentry/react": "^8.48.0", "@sentry/remix": "^8", "@splinetool/runtime": "^1.0.93", "@tanstack/react-query": "^5.28.4", "@tomo-inc/tomo-telegram-sdk": "^1.0.16-beta.2", "@ton/crypto": "^3.3.0", "@use-gesture/react": "^10.3.1", "@use-gesture/vanilla": "^10.3.1", "@uxuycom/web3-tg-sdk": "^0.2.5", "@wagmi/core": "^2.13.4", "accept-language-parser": "^1.5.0", "ahooks": "^3.7.11", "axios": "^1.6.7", "bignumber.js": "^9.1.2", "bitcoin-address-validation": "^2.2.3", "bitcoinjs-lib": "^6.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "compressorjs": "^1.2.1", "date-fns": "^3.6.0", "decimal.js": "^10.4.3", "embla-carousel-autoplay": "^8.0.2", "embla-carousel-react": "^8.0.0-rc23", "ethereumjs-util": "^7.1.5", "ethers": "^5.7.2", "framer-motion": "^11.0.13", "gsap": "^3.12.5", "i18next": "^23.11.2", "i18next-browser-languagedetector": "^7.2.1", "isbot": "4", "jotai": "^2.7.2", "js-md5": "^0.8.3", "jsrsasign": "^11.1.0", "ky": "^1.5.0", "leva": "^0.9.35", "lodash.partial": "^4.2.1", "lucide-react": "^0.523.0", "md5js": "^1.0.7", "nanoid": "^5.0.6", "next-themes": "^0.3.0", "qr-code-styling": "^1.6.0-rc.1", "react": "18.3.0-canary-a870b2d54-20240314", "react-audio-player": "^0.17.0", "react-countdown": "^2.3.5", "react-countup": "^6.5.3", "react-device-detect": "^2.2.3", "react-dom": "18.3.0-canary-a870b2d54-20240314", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.51.0", "react-i18next": "^14.1.0", "react-jazzicon": "^1.0.4", "react-player": "^2.16.0", "react-toastify": "^10.0.5", "recharts": "^3.0.2", "remix-hook-form": "^4.2.0", "remix-i18next": "^6.1.0", "remix-utils": "^7.5.0", "sats-connect": "^2.8.0", "simplebar-react": "^3.2.6", "sonner": "^1.4.41", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "tp-js-sdk": "^3.7.11", "ua-parser-js": "^0.7.39", "use-scramble": "^2.2.15", "vaul": "^0.9.0", "viem": "^2.21.54", "wagmi": "^2.12.7", "wagmi-permit": "^1.0.19", "zod": "^3.22.4"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240222.0", "@remix-run/dev": "^2.8.0", "@sentry/vite-plugin": "^2.22.7", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^16.0.0", "@types/accept-language-parser": "^1.5.6", "@types/compression": "^1.7.5", "@types/jsrsasign": "^10.5.13", "@types/lodash.partial": "^4.2.9", "@types/node": "^20.11.19", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "@vitest/ui": "^1.6.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^24.1.0", "patch-package": "^8.0.0", "postcss": "^8.4.35", "postcss-preset-env": "^9.5.4", "prettier": "^3.2.5", "remix-flat-routes": "^0.6.4", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "unplugin-fonts": "^1.1.1", "vite": "^5.1.4", "vite-plugin-node-polyfills": "^0.21.0", "vite-tsconfig-paths": "^4.3.1", "vitest": "^1.6.0", "wrangler": "^3.35.0"}, "packageManager": "yarn@1.22.1", "resolutions": {"viem": "^2.21.54"}}