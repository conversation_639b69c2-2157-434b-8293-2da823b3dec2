@font-face {
  font-family: 'Hudson NY Pro';
  src:
    local('Hudson NY Pro Regular'),
    local('Hudson-NY-Pro-Regular'),
    url('./assets/fonts/HudsonNYPro-Regular.woff2') format('woff2'),
    url('./assets/fonts/HudsonNYPro-Regular.woff') format('woff'),
    url('./assets/fonts/HudsonNYPro-Regular.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Hammer Tongs';
  src:
    local('Hammer Tongs'),
    url('./assets/fonts/Hammer & Tongs.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tomkin Narrow';
  src:
    local('Tomkin Narrow Regular'),
    local('Tomkin-Narrow-Regular'),
    url('./assets/fonts/TomkinNarrow-ExtraBold.woff2') format('woff2'),
    url('./assets/fonts/TomkinNarrow-ExtraBold.woff') format('woff'),
    url('./assets/fonts/TomkinNarrow-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tomkin Narrow';
  src:
    local('Tomkin Narrow Regular'),
    local('Tomkin-Narrow-Regular'),
    url('./assets/fonts/TomkinNarrow-Bold.woff2') format('woff2'),
    url('./assets/fonts/TomkinNarrow-Bold.woff') format('woff'),
    url('./assets/fonts/TomkinNarrow-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tomkin Narrow';
  src:
    local('Tomkin Narrow Regular'),
    local('Tomkin-Narrow-Regular'),
    url('./assets/fonts/TomkinNarrow-Medium.woff2') format('woff2'),
    url('./assets/fonts/TomkinNarrow-Medium.woff') format('woff'),
    url('./assets/fonts/TomkinNarrow-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tomkin Narrow';
  src:
    local('Tomkin Narrow Regular'),
    local('Tomkin-Narrow-Regular'),
    url('./assets/fonts/TomkinNarrow-Regular.woff2') format('woff2'),
    url('./assets/fonts/TomkinNarrow-Regular.woff') format('woff'),
    url('./assets/fonts/TomkinNarrow-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tomkin Narrow';
  src:
    url('./assets/fonts/TomkinNarrow-SemiLight.woff2') format('woff2'),
    url('./assets/fonts/TomkinNarrow-SemiLight.woff') format('woff'),
    url('./assets/fonts/TomkinNarrow-SemiLight.ttf') format('truetype');
  font-weight: 350;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tomkin Narrow';
  src:
    url('./assets/fonts/TomkinNarrow-Light.woff2') format('woff2'),
    url('./assets/fonts/TomkinNarrow-Light.woff') format('woff'),
    url('./assets/fonts/TomkinNarrow-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tomkin Narrow';
  src:
    url('./assets/fonts/TomkinNarrow-ExtraLight.woff2') format('woff2'),
    url('./assets/fonts/TomkinNarrow-ExtraLight.woff') format('woff'),
    url('./assets/fonts/TomkinNarrow-ExtraLight.ttf') format('truetype');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tomkin Narrow';
  src:
    url('./assets/fonts/TomkinNarrow-Thin.woff2') format('woff2'),
    url('./assets/fonts/TomkinNarrow-Thin.woff') format('woff'),
    url('./assets/fonts/TomkinNarrow-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 222 17% 73%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --card-background: 0 0% 12%;
    --card-border: 0 0% 21%;

    --popover: 0 0% 11%;
    --popover-foreground: 222 5% 43%;

    --primary: 25 79% 50%;
    --primary-foreground: 25 70% 23%;
    --primary-divider: 25 79% 46%;

    --secondary: 222 17% 73%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 228 7% 58%;

    --accent: var(--primary);
    --accent-foreground: 0 0% 100%;

    --destructive: 355 100% 74%;
    --destructive-foreground: 210 40% 98%;

    --divider: 210 10% 12%;
    --border: 214.3 31.8% 91.4%;

    --input: 0 0% 21%;
    --input-foreground: 228 7% 58%;
    --input-background: 0 0% 11%;

    --button-error: 0 79% 39%;

    --ring: 222.2 84% 4.9%;
    --indicator: 0 0% 36%;
    --success: 101 61% 62%;
    --error: 355 100% 74%;

    --radius: 0.5rem;
    --gradient-white: linear-gradient(180deg, #ffffff 0%, #f7f7f0 100%);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --divider: 210 10% 12%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --indicator: 0 0% 34%;
  }
}

@layer base {
  * {
    @apply bl-border-border;
  }

  body {
    @apply bl-bg-background bl-text-foreground;
  }
}

@layer components {
  * {
    -webkit-font-smoothing: antialiased;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bl-w-screen;
  }

  .bl-container {
    @apply bl-px-5 md:bl-px-0;
  }

  /* Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type='number'] {
    -moz-appearance: textfield;
  }

  .text-gradient {
    background: linear-gradient(269deg, #7d3d0f -20.8%, #e36e1b 107.25%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .toLeft {
    background: linear-gradient(269deg, #e36e1b 5.06%, #7d3d0f 113.31%);
  }

  .winner-shadow {
    box-shadow: 0px -0.4px 3px 0px rgba(0, 0, 0, 0.05) inset;
  }

  .btn-mask {
    @apply bl-overflow-hidden;
    -webkit-mask: linear-gradient(
        -45deg,
        transparent 7.313708498984761px,
        white 8.313708498984761px,
        white calc(100% - 7.313708498984761px),
        transparent calc(100% - 8.313708498984761px)
      ),
      linear-gradient(
        45deg,
        transparent -1px,
        white 0px,
        white calc(100% - -1px),
        transparent calc(100% - 0px)
      );
    -webkit-mask-composite: source-in, xor;
    mask-composite: intersect;
  }

  .btn-mask.btn-xs {
    @apply bl-overflow-hidden;
    -webkit-mask: linear-gradient(
        -45deg,
        transparent 5.313708498984761px,
        white 6.313708498984761px,
        white calc(100% - 5.313708498984761px),
        transparent calc(100% - 6.313708498984761px)
      ),
      linear-gradient(
        45deg,
        transparent -1px,
        white 0px,
        white calc(100% - -1px),
        transparent calc(100% - 0px)
      );
    -webkit-mask-composite: source-in, xor;
    mask-composite: intersect;
  }

  .bl-flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ticker-container {
    width: 100%;
    overflow: hidden;
  }

  .outline-text {
    color: transparent;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: #6e6e6e;
    -webkit-text-stroke: 1px #6e6e6e;
    text-stroke: 1px #6e6e6e;
  }

  .outline-text-secondary {
    color: transparent;
    background-image: linear-gradient(180deg, #aeb5c5 5.83%, rgba(84, 87, 95, 0.1) 83.33%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: #aeb5c5;
  }

  .ticker-text {
    white-space: nowrap;
    -webkit-animation: scrollLeft 10s linear infinite;
    animation: scrollLeft 10s linear infinite;
  }

  @-webkit-keyframes scrollLeft {
    0% {
      transform: translateX(100%);
    }

    100% {
      transform: translateX(-100%);
    }
  }

  @keyframes scrollLeft {
    0% {
      transform: translateX(100%);
    }

    100% {
      transform: translateX(-100%);
    }
  }

  .draw-ticker-container {
    /* width: 100%; */
    overflow: hidden;
    position: relative;
  }

  .draw-ticker-text {
    display: flex;
    white-space: nowrap;
    -webkit-animation: scrollLeftDraw 100s linear infinite;
    animation: scrollLeftDraw 100s linear infinite;
  }

  @-webkit-keyframes scrollLeftDraw {
    0% {
      transform: translateX(0%);
    }

    100% {
      transform: translateX(-50%);
    }
  }

  @keyframes scrollLeftDraw {
    0% {
      transform: translateX(0%);
    }

    100% {
      transform: translateX(-50%);
    }
  }

  .animate-marquee {
    -webkit-animation: marquee 6s linear infinite;
    animation: marquee 6s linear infinite;
  }

  /* .animate-marquee:hover {
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
  } */

  @-webkit-keyframes marquee {
    0% {
      transform: translateX(100%);
    }

    100% {
      transform: translateX(-100%);
    }
  }

  @keyframes marquee {
    0% {
      transform: translateX(100%);
    }

    100% {
      transform: translateX(-100%);
    }
  }

  .dot-loader {
    width: 24px;
    aspect-ratio: 2;
    --_g: no-repeat radial-gradient(circle closest-side, hsl(var(--primary)) 90%, #0000);
    background:
      var(--_g) 0% 50%,
      var(--_g) 50% 50%,
      var(--_g) 100% 50%;
    background-size: calc(100% / 3) 50%;
    -webkit-animation: l3 1s infinite linear;
    animation: l3 1s infinite linear;
  }

  @-webkit-keyframes l3 {
    20% {
      background-position:
        0% 0%,
        50% 50%,
        100% 50%;
    }

    40% {
      background-position:
        0% 100%,
        50% 0%,
        100% 50%;
    }

    60% {
      background-position:
        0% 50%,
        50% 100%,
        100% 0%;
    }

    80% {
      background-position:
        0% 50%,
        50% 50%,
        100% 100%;
    }
  }

  @keyframes l3 {
    20% {
      background-position:
        0% 0%,
        50% 50%,
        100% 50%;
    }

    40% {
      background-position:
        0% 100%,
        50% 0%,
        100% 50%;
    }

    60% {
      background-position:
        0% 50%,
        50% 100%,
        100% 0%;
    }

    80% {
      background-position:
        0% 50%,
        50% 50%,
        100% 100%;
    }
  }

  @-webkit-keyframes my-fade-left {
    20% {
      opacity: 0;
      transform: translateX(-100%);
    }

    100% {
      opacity: 1;
      transform: translateX(0%);
    }
  }

  @keyframes my-fade-left {
    20% {
      opacity: 0;
      transform: translateX(-100%);
    }

    100% {
      opacity: 1;
      transform: translateX(0%);
    }
  }

  @-webkit-keyframes my-fade-right {
    0% {
      transform: translateX(100%);
    }

    100% {
      transform: translateX(0%);
    }
  }

  @keyframes my-fade-right {
    0% {
      transform: translateX(100%);
    }

    100% {
      transform: translateX(0%);
    }
  }

  @-webkit-keyframes bg-to-r {
    from {
      background-position: left;
    }

    to {
      background-position: right;
    }
  }

  @keyframes bg-to-r {
    from {
      background-position: left;
    }

    to {
      background-position: right;
    }
  }

  @-webkit-keyframes wave-skew {
    0% {
      transform: skewY(0deg);
    }

    50% {
      transform: skewY(-2deg);
    }

    100% {
      transform: skewY(0deg);
    }
  }

  @keyframes wave-skew {
    0% {
      transform: skewY(0deg);
    }

    50% {
      transform: skewY(-2deg);
    }

    100% {
      transform: skewY(0deg);
    }
  }

  .wave-skew {
    -webkit-animation: wave-skew 3s infinite;
    animation: wave-skew 3s infinite;
  }

  .scroll-to-show {
    -webkit-animation: my-fade-left linear;
    animation: my-fade-left linear;
    view-timeline-inset: -20%;
    animation-timeline: view();
    animation-range: -10vh 75vh;
  }

  .scroll-to-show-right {
    -webkit-animation: my-fade-right linear;
    animation: my-fade-right linear;
    view-timeline-inset: -20%;
    animation-timeline: view();
    animation-range: -10vh 75vh;
  }

  @-webkit-keyframes diagonal-gradient-animation {
    0% {
      background: linear-gradient(
        222.72deg,
        #eee2f1 15.29%,
        #ccaad5 21.34%,
        #fbf4f4 30.24%,
        #8dc7d8 43.62%,
        #65bfe0 58.48%,
        #aca2db 75.42%,
        #81c1d9 96.27%
      );
    }

    10% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 17.34%,
        #aca2db 37.86%,
        #65bfe0 54.22%,
        #8dc7d8 69.12%,
        #fbf4f4 82.36%,
        #ccaad5 91.07%,
        #eee2f1 96.98%
      );
    }

    20% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 19.39%,
        #aca2db 39.57%,
        #65bfe0 55.36%,
        #8dc7d8 70.3%,
        #fbf4f4 83.41%,
        #ccaad5 91.92%,
        #eee2f1 97.69%
      );
    }

    30% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 21.44%,
        #aca2db 41.27%,
        #65bfe0 56.5%,
        #8dc7d8 71.47%,
        #fbf4f4 84.46%,
        #ccaad5 92.77%,
        #eee2f1 98.4%
      );
    }

    40% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 23.48%,
        #aca2db 42.98%,
        #65bfe0 57.64%,
        #8dc7d8 72.65%,
        #fbf4f4 85.51%,
        #ccaad5 93.61%,
        #eee2f1 99.11%
      );
    }

    50% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 25.53%,
        #aca2db 44.68%,
        #65bfe0 58.78%,
        #8dc7d8 73.83%,
        #fbf4f4 86.55%,
        #ccaad5 94.46%,
        #eee2f1 99.82%
      );
    }

    60% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 27.58%,
        #aca2db 46.39%,
        #65bfe0 59.92%,
        #8dc7d8 75.01%,
        #fbf4f4 87.59%,
        #ccaad5 95.3%,
        #eee2f1 100.53%
      );
    }

    70% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 29.62%,
        #aca2db 48.1%,
        #65bfe0 61.06%,
        #8dc7d8 76.19%,
        #fbf4f4 88.64%,
        #ccaad5 96.14%,
        #eee2f1 101.24%
      );
    }

    80% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 31.67%,
        #aca2db 49.8%,
        #65bfe0 62.2%,
        #8dc7d8 77.37%,
        #fbf4f4 89.68%,
        #ccaad5 96.98%,
        #eee2f1 101.95%
      );
    }

    90% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 33.72%,
        #aca2db 51.5%,
        #65bfe0 63.34%,
        #8dc7d8 78.55%,
        #fbf4f4 90.72%,
        #ccaad5 97.83%,
        #eee2f1 102.66%
      );
    }

    100% {
      background: linear-gradient(
        222.72deg,
        #eee2f1 15.29%,
        #ccaad5 21.34%,
        #fbf4f4 30.24%,
        #8dc7d8 43.62%,
        #65bfe0 58.48%,
        #aca2db 75.42%,
        #81c1d9 96.27%
      );
    }
  }

  @keyframes diagonal-gradient-animation {
    0% {
      background: linear-gradient(
        222.72deg,
        #eee2f1 15.29%,
        #ccaad5 21.34%,
        #fbf4f4 30.24%,
        #8dc7d8 43.62%,
        #65bfe0 58.48%,
        #aca2db 75.42%,
        #81c1d9 96.27%
      );
    }

    10% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 17.34%,
        #aca2db 37.86%,
        #65bfe0 54.22%,
        #8dc7d8 69.12%,
        #fbf4f4 82.36%,
        #ccaad5 91.07%,
        #eee2f1 96.98%
      );
    }

    20% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 19.39%,
        #aca2db 39.57%,
        #65bfe0 55.36%,
        #8dc7d8 70.3%,
        #fbf4f4 83.41%,
        #ccaad5 91.92%,
        #eee2f1 97.69%
      );
    }

    30% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 21.44%,
        #aca2db 41.27%,
        #65bfe0 56.5%,
        #8dc7d8 71.47%,
        #fbf4f4 84.46%,
        #ccaad5 92.77%,
        #eee2f1 98.4%
      );
    }

    40% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 23.48%,
        #aca2db 42.98%,
        #65bfe0 57.64%,
        #8dc7d8 72.65%,
        #fbf4f4 85.51%,
        #ccaad5 93.61%,
        #eee2f1 99.11%
      );
    }

    50% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 25.53%,
        #aca2db 44.68%,
        #65bfe0 58.78%,
        #8dc7d8 73.83%,
        #fbf4f4 86.55%,
        #ccaad5 94.46%,
        #eee2f1 99.82%
      );
    }

    60% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 27.58%,
        #aca2db 46.39%,
        #65bfe0 59.92%,
        #8dc7d8 75.01%,
        #fbf4f4 87.59%,
        #ccaad5 95.3%,
        #eee2f1 100.53%
      );
    }

    70% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 29.62%,
        #aca2db 48.1%,
        #65bfe0 61.06%,
        #8dc7d8 76.19%,
        #fbf4f4 88.64%,
        #ccaad5 96.14%,
        #eee2f1 101.24%
      );
    }

    80% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 31.67%,
        #aca2db 49.8%,
        #65bfe0 62.2%,
        #8dc7d8 77.37%,
        #fbf4f4 89.68%,
        #ccaad5 96.98%,
        #eee2f1 101.95%
      );
    }

    90% {
      background: linear-gradient(
        222.72deg,
        #81c1d9 33.72%,
        #aca2db 51.5%,
        #65bfe0 63.34%,
        #8dc7d8 78.55%,
        #fbf4f4 90.72%,
        #ccaad5 97.83%,
        #eee2f1 102.66%
      );
    }

    100% {
      background: linear-gradient(
        222.72deg,
        #eee2f1 15.29%,
        #ccaad5 21.34%,
        #fbf4f4 30.24%,
        #8dc7d8 43.62%,
        #65bfe0 58.48%,
        #aca2db 75.42%,
        #81c1d9 96.27%
      );
    }
  }

  .bl-bg-blur {
    background: rgba(0, 0, 0, 0.9);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
  }

  .dialogScrollBar {
    padding-left: 4px;

    .simplebar-wrapper {
      margin: 0 !important;

      .simplebar-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        button {
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0px;
          }
        }
      }
    }

    .simplebar-track {
      background-color: #363636;
      overflow: visible;
      border: 1px solid hsl(var(--primary));

      &.simplebar-vertical {
        width: 4px;
        height: 100%;
      }

      &.simplebar-horizontal {
        width: 100%;
        height: 4px;
      }

      .simplebar-scrollbar {
        width: 4px;
        left: -1px;
        background-color: #000000;
      }
    }
  }

  .scrollBar-black-primary {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background-color: #000;
      border-radius: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #e36e1b;
      border-radius: 6px;
    }

    &::-webkit-scrollbar-thumb:hover {
      /* background-color: rgba($color: #E36E1B, $alpha: 0.6); */
    }
  }

  .always-show-scrollbar {
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
  }

  .always-show-scrollbar::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 4px;
    height: 4px;
  }

  .always-show-scrollbar::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #e3631b;
    -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
  }

  .btcfi-v2-article {
    @apply bl-text-base lg:bl-text-xl;

    h3 {
      @apply bl-text-sm lg:bl-text-base;
    }
    p {
      @apply bl-mt-8 bl-text-white;
      em {
        @apply bl-text-primary bl-not-italic;
      }
    }
    ul {
      @apply bl-list-disc bl-list-inside bl-space-y-2 bl-ml-2 bl-mt-2;
    }
  }

  .btcfi-v2-risk {
    h3 {
      @apply bl-text-sm;
    }

    h4 {
      @apply bl-text-white bl-text-sm bl-flex bl-items-center bl-gap-1.5 bl-mt-5;
      @apply before:bl-size-[3px] before:bl-bg-white before:bl-content-[''];
    }

    p {
      @apply bl-text-secondary bl-text-sm bl-mt-2;
    }
  }
}
