export default {
  title: 'Bitlayer',
  meta: {
    description: 'The First Bitcoin Layer 2 Based on the BitVM Paradigm',
  },
  common: {
    readMore: 'Read More',
    learnMore: 'Learn More',
    upload: 'Upload',
    submit: 'Submit',
    return: 'Return',
    send: 'Send',
    back: 'Back',
    coming: 'Coming soon',
    connect: 'Connect Wallet',
    disconnect: 'Disconnect',
    switchChain: 'Switch to {{chainName}}',
    insufficientBalance: 'Insufficient balance',
    search: 'search',
    toTop: 'Return to Top',
    getGas: 'Get Bitlayer Gas',
    bridge: 'Bridge & Earn',
    connectDesc: 'Connect your wallet to transfer tokens',
    MesonDes: 'Safe, Costless & Instant Cross-chain for Stablecoins',
    OwltoDes: 'Owlto Finance is an intent-centric interoperability protocol',
    OrbiterDes: 'Orbiter Finance is a decentralized cross-rollup bridge',
    BoolBridgeDes: 'Bool Bridge is a decentralized bridge that connects Bitcoin and Layer 2.',
    OmnityBridgeDes: 'Omnity is a fully on-chain and easy-to-use bridge for Bitcoin token holders.',
    miniBridgeDes:
      'Minibridge is a "zero-fee" cross-chain tool that offers multi-chain fast-transaction support for BTC/EVM/non-EVM.',
    login: 'Login',
    noData: 'No Data',
    confirm: 'Confirm',
    Ok: 'OK',
    cancel: 'Cancel',
    reject: 'Reject',
    website: 'Website',
    share: 'Share',
    more: 'More',
    less: 'Less',
    flash: 'Flash Bridge-In',
    getIt: 'Get it',
    congratulations: 'Congratulations!',
    error: {
      gasLess: 'Insufficient gas funds for this transaction',
      transactionFailed: 'Blockchain transaction failed',
    },
  },
  resources: {
    github: 'https://github.com/bitlayer-org',
    discord: 'https://discord.gg/bitlayer',
    x: 'https://twitter.com/BitLayerLabs',
    linkedin: 'https://bit.ly/42B6v15',
    telegram: 'https://t.me/bitlayerofficial',
    medium: 'https://medium.com/@Bitlayer',
    rankRule:
      'https://medium.com/@Bitlayer/bitlayer-provides-20m-airdrop-for-dapp-leaderboard-competition-rewarding-ecosystem-projects-and-87ed3dc76b94',
  },
  navigation: {
    links: {
      opvm: 'OpVM',
      bitvm: 'BitVM Stack',
      developers: 'Developers',
      users: 'Users',
      bitlayerV2: 'Bitlayer V2',
      ecosystem: 'Ecosystem',
      userCenter: 'Racer Center',
      superDraw: 'Super Racer Draw',
      badge: 'My Badges',
      btcfi: 'BTC Fi',
      btcfiDesc: 'Best yield of BTC and BTC layer2',
      resources: 'Resources',
      contact: 'Contact Us',
      mainnet: 'Mainnet',
      bridgeEarn: 'Bridge & Earn',
      usdcChange: 'USDC Change',
      gas: 'Get Gas',
      startBuilding: 'Start Building',
      btrScan: 'Bitlayer(BTR) Scan',
      addMainnet: 'Add Bitlayer Mainnet',
      testnet: 'Testnet',
      faucet: 'Faucet',
      testnetBridge: 'Testnet Bridge',
      testnetBridgeHint: 'Bitlayer Testnet Bridge',
      testnetScan: 'Testnet Scan',
      addTestnet: 'Add Bitlayer Testnet',
      language: 'Language',
      getGas: 'Get Bitlayer Gas',
      readyPlayerOne: 'Ready Player One',
      career: 'Career',
      luckyHelmet: 'Lucky Helmet',
      dAppsLeaderboard: 'DApps Leaderboard',
      hint1: '$50,000,000 Airdrop',
      hint2: 'Be a driver. Earn from bitlayer',
      hint3: 'Vote for Your Favorite Project',
      dappCenterEcosystem: 'Win Bitlayer points & dapp airdrops',
      brandKit: 'Brand Kit',
      whitepaper: 'Whitepaper 2.0',
      leaderboard: 'Leaderboard',
      dappCenter: 'DApp Center',
      dappCenterHint: 'Bitlayer DApp Ranking List',
      miningGala: 'Mining Gala',
      miningGala3: 'Mining Gala III',
      bridge: 'Bridge',
      flash: 'Flash Bridge-In',
      finalityBridge: 'BitVM Bridge',
      bitvmBridge: 'BitVM Bridge',
      bitcoinRollup: 'Bitcoin Rollup',
      bFStarks: 'BF-STARKs',
      bitVMContribution: 'BitVM Contribution',
      product: 'Product',
      innovation: 'Innovation',
      quickJump: 'Quick Jump',
      developersSection: {
        hint: 'Start building on Bitlayer chain',
        documentation: 'Documentations',
        documentationHint: 'Supporting docs for developers',
        tools: {
          title: 'Boost Tools',
          hint: 'Developer tools for your projects',
          mainnetScan: 'Mainnet Scan',
          mainnetScanHint: 'Bitlayer(BTR) Mainnet Scan',
          testnetScan: 'Testnet Scan',
          testnetScanHint: 'Bitlayer(BTR) Testnet Scan',
          faucet: 'Faucet',
          faucetHint: 'Pilot tokens on Bitlayer chain',
          theGraph: 'The Graph',
          theGraphHint: 'Index and search your data on Bitlayer',
          multisigWallet: 'Multisig Wallet',
          multisigWalletHint: 'Store your private keys, safe and easy',
        },
        security: {
          title: 'Security',
          hint: 'Security support',
          dappSecurityManual: 'Dapp Security Manual',
          dappSecurityManualHint: 'Tutorial to build security Dapp',
          auditAlliance: 'Audit Alliance',
          auditAllianceHint: 'Audit Alliance',
          securityNetwork: 'Security Network',
          securityNetworkHint: 'Security Network',
        },
        developerSupport: {
          title: 'Operation Supports',
          hint: 'Grants, funding, incentive and supports',
          readyPlayerOne: 'Ready player I',
          readyPlayerOneHint: 'Trade to earn up to $100,000 prize pool',
          luckyHelmet: 'Lucky Helmet',
          luckyHelmetHint: 'Unlock $50,000,000 for early builders',
          miningGala: 'Mining Gala',
          miningGalaHint: 'Win Bitlayer rewards & dapp airdrops',
          leaderboard: 'Leaderboard',
          leaderboardHint: 'Bitlayer Leaderboard Competition Epoch 1',
        },
        hackathon: 'Hackathon',
        hackathonHint: 'Supporting docs for developers',
        github: 'Github',
        githubHint: 'Explore Bitlayer tech repositories',
        devCommunities: 'Dev Communities',
        devCommunitiesHint: 'Join developers chats',
        telegram: 'Telegram',
        opvm: 'OpVM',
        opvmHint: 'Make Bitcoin verifiable',
        aiGrant: 'Bitlayer AI Boostcamp',
        aiGrantHint: 'Reimagine Bitcoin + AI',
      },
    },
    footer: {
      Participate: 'Participate',
      ReadyPlayerOne: 'Ready Player One',
      Build: 'Build',
      BuildingBitlayer: 'Building Bitlayer',
      Faucet: 'Faucet',
      SupportedWallets: 'Supported Wallets',
      TheGraph: 'The Graph',
      MainScan: 'Bitlayer(BTR) Scan',
      TestScan: 'Testnet Scan',
      About: 'About',
      Solutions: 'Solutions',
      Roadmap: 'Roadmap',
      EventNews: 'Event & News',
      Jobs: 'Jobs',
      Community: 'Community',
      blog: 'Blog',
      Podcast: 'Podcast',
      Operation: 'Operation',
    },
    walletDrawer: {
      wrongNet: 'Please switch to {{chainName}}',
      noNFT: 'No Collection',
    },
  },
  pages: {
    home: {
      title: {
        notice: 'Bitlayer Ready Player I is Now Live! Vote For Points & WLs Now!',
        slogon1: 'MAKE',
        slogon2: 'BITCOIN',
        slogon3: 'VERIFIABLE',
        powering: 'Powering Bitcoin DeFi',
        explore: 'Explore BitVM Bridge',
        btrBridge: 'Bitlayer Bridge',
        extra: 'Bridge into Bitlayer Network',
        the: 'THE',
        first: 'FIRST',
        bitvm: 'BITVM',
        startBuilding: 'Start Building',
        bridgeEarn: 'Bridge & Earn',
        bridge: 'Bridge',
        competition: 'Bitlayer DApp Center',
        dapp100:
          "Earn Rewards from <span class='bl-text-xs md:bl-text-2xl bl-text-primary bl-font-[600]'>100+</span> Dapps!",
        gasFee: 'Median Gas Fee:',
        whitePaper: 'Bitlayer Whitepaper',
        leaderboard:
          "Check your eligibility for <span class='bl-text-xs md:bl-text-2xl bl-text-white bl-font-[600] bl-px-1'> Gems airdrop </span> now!",
      },
      spotlight: {
        spotlight: 'Spotlight',
        readMore: 'Read More',
        slideshow: {
          item1: {
            type: 'Blog',
            title: 'BitVM Bridge',
            content: 'Gateway for Bitcoin DeFi.',
          },
          item2: {
            type: 'Blog',
            title: 'Bitlayer V2',
            content: 'A Bitcoin Layer 2 based on the BitVM paradigm.',
          },
          item3: {
            type: 'Airdrop Event',
            title: 'NFT Holder Bonus',
            content: 'Special $BTR bonus exclusively for Lucky Helmet NFT holders.',
          },
          item4: {
            type: 'Airdrop Event',
            title: 'Super Racer Draw',
            content: 'Assemble cars to get 200% $BTR refund.',
          },
          item5: {
            type: 'Airdrop Event',
            title: 'Bitlayer DApp Center',
            content:
              'Celebrate the launch of Bitlayer DApp Center with exclusive airdrop of up to 100,000,000 Bitlayer Points.',
          },
          item6: {
            type: 'Airdrop Event',
            title: 'Bitlayer Racer Center',
            content:
              'Bitlayer racer center is now live! Explore tasks and mini-games to earn Bitlayer gems and points!',
          },
        },
      },

      liveData: {
        liveData: 'Live Data',
        explore: 'explore',
        'the community': 'the community',
        'of bitlayer': 'of bitlayer',
      },
      solutions: {
        solutions: 'Solutions',
        version: 'Vision',
        infrastructure: 'The ULTIMATE Bitcoin Defi Infrastructure',
        title: "WHAT BITCOIN LAYER 2'S FUTURE SHOULD BE",
        subtitle: 'The key pillar solutions for the future of Bitcoin ecosystem',
        items: [
          {
            title: 'BITCOIN SECURITY',
            text: 'Inherits Bitcoin security via BitVM',
          },
          {
            title: 'TRUSTLESS BRIDGE',
            text: 'Combining DLC & BitVM, bringing groundbreaking models that supersede traditional multisig',
          },
          {
            title: 'REALTIME VMs',
            text: 'Supports multiple VMs, enables a 100% EVM-Compatible Environment',
          },
        ],
        whitepaper: {
          title: 'DOWNLOAD WHITEPAPER',
          button: 'Whitepaper',
        },
      },
      bitvm: {
        bitvm: 'BitVM',
        what: {
          title: 'What is BitVM',
          describe:
            'BitVM is a revolutionary paradigm to express Turing-complete Bitcoin contracts, using an optimistic validation scheme.',
        },
        how: {
          title: 'How BitVM Works',
          describe:
            'BitVM employs a pre-signed transaction graph to emulate smart contracts on Bitcoin, enabling the validation of off-chain computations through the optimistic validation of the zero-knowledge verifier.',
        },
      },
      bitvmBridge: {
        bitvmBridge: 'BitVM Bridge',
        title: 'Gateway for Bitcoin DeFi',
        items: [
          {
            title: 'Best Security',
            subtitle: '- Existential Honesty -',
            text: 'Best trust assumption (1-of-N) is achieved through the existential honesty of system roles.',
          },
          {
            title: 'Full Programmability',
            subtitle: '- Peg-BTC (YBTC) -',
            text: 'Peg-BTC (YBTC) is a fully programmable asset that seamlessly integrates into various programmable environments, thanks to the multi-chain support provided by BitVM Bridge.',
          },
          {
            title: 'Yield-Bearing',
            subtitle: '- Sustainable Yield -',
            text: 'Provides more value-added channels for holders through native or integrated yield products.',
          },
        ],
      },
      bitlayerNetwork: {
        bitlayerNetwork: 'Bitlayer Network',
        title: 'Engine for Bitcoin DeFi',
        items: [
          {
            title: 'Bitcoin Security',
            subtitle: '- Bitcoin Rollup -',
            text: 'Combining BitVM-style smart contracts with zero-knowledge proofs to achieve finality on Bitcoin.',
          },
          {
            title: 'Trust-Minimized Bridge',
            subtitle: '- BitVM Bridge -',
            text: 'BitVM Bridge achieves the best trust assumption (1-of-N).',
          },
          {
            title: 'Realtime EVM',
            subtitle: '- RtEVM -',
            text: 'Delivering near real-time EVM performance, with cutting-edge parallel execution engine and blockchain-optimized database.',
          },
        ],
      },
      innovations: {
        innovations: 'Innovations',
        items: [
          {
            title: 'BITCOIN SECURITY',
            subtitle: '- Bitcoin Rollup -',
            text: 'Combining BitVM-style smart contracts with zero-knowledge proofs to achieve finality on Bitcoin.',
          },
          {
            title: 'TRUST-MINIMIZED BRIDGE',
            subtitle: '- BitVM Bridge -',
            text: 'A trust-minimized BTC bridge powered by BitVM technology,serving as the cornerstone of modern BTCFi.',
            imageDesc:
              'Combinning with atomic swap,BTC can be moved between Bitcoin L1 and L2s trustlessly,superseding the old multisig way.',
          },
          {
            title: 'REALTIME EVM',
            subtitle: '- RtEVM -',
            text: 'Delivering near real-time EVM performance,with cutting-edge parallel execution engine and blockchain-optimized database.',
            imageDesc1:
              'Scheduler optimistically dispatches transactions to multiple EVM excutors, fully utilizing multi-thread.',
            imageDesc2:
              'Attach sharded KV stores to a scalable MPT trie, enabling unlimited capacity and extremely fast state access.',
          },
        ],
      },
      roadmap: {
        roadmap: 'Roadmap',
        title: 'The Journey of Bitcoin',
        title2: 'The Journey of Bitlayer',
        achieved: '(Achieved)',
        staging: '(Staging)',
        bridge: {
          points: [
            {
              title: 'BitVM Bridge Testnet Launch',
              description: 'Mint Features',
              date: 'DEC.2024',
            },
            {
              title: 'New Functions Launch',
              description: 'Unmint, Reclaim Process',
              date: 'FEB.2025',
            },
            {
              title: 'Mainnet Launch',
              date: 'May 2025',
            },
            {
              title: 'Support for Additional Chains',
              date: '2025 H2',
            },
          ],
        },
        network: {
          points: [
            {
              title: 'Bitlayer PoS, the Foundation',
              description:
                'Security model combines PoS and multisig<br/>Cross-chain functionality<br/>EVM compatibility',
              date: 'April 2024',
            },
            {
              title: 'Bitlayer Rollup, BitVM Based',
              description:
                'On-chain STF verification<br/>Trust-minimized BTC Bridge<br/>Seamless migration',
              date: 'Q1/Q2 2025',
            },
            {
              title: 'Bitlayer Rollup, Super Charged',
              description: 'Unmatched performance<br/>Lightning-fast confirmations',
              date: 'Q4 2025',
            },
          ],
        },

        journeys: [
          {
            title: 'BITLAYER MAINNET V1',
            texts: ['Bitlayer PoS'],
            subtitle: 'APR.2024',
          },
          {
            title: 'BITLAYER MAINNET V2',
            texts: ['Bitlayer Rollup,BitVM based', 'BitVM Bridge', 'Bitcoin Rollup'],
            subtitle: 'Q1/Q2.2025',
          },
          {
            title: 'BITLAYER MAINNET V3',
            texts: ['Bitlayer Rollup,Super Charged', 'Realtime EVM'],
            subtitle: 'Q4.2025',
          },
        ],
      },
      events: {
        tag: 'Event & News',
        title: 'Bitlayer Ecosystem Event & News',
        subtitle: 'Follow up our updates',
        items: [
          'Bitcoin 2024 Nashville',
          'Korea Blockchain Week',
          'Token 2049',
          'Bitlayer Night Korea',
        ],
      },
      jobs: {
        tag: 'Jobs',
        title: 'Job Openings',
        subtitle: "Let's Make Bitcoin History Together!",
        positions: [
          {
            title: 'Head of Developer Relations_',
            skills: [
              'Responsible for expanding and maintaining developer relations within the public blockchain ecosystem',
              'Handle technical communication and developer education with various global developer technical communities and ecosystems.',
            ],
          },
          {
            title: 'Protocol Researcher_',
            skills: [
              'Conduct cutting-edge research on blockchain based on industry pain points.',
              'Write high-quality research reports and papers.',
            ],
          },
        ],
        more: 'Explore open positions',
      },
      community: {
        tag: 'Community',
        title: 'Join the community of the future',
        subtitle: 'Stay active with Bitlayer',
        items: [
          {
            title: 'Contribute to Bitlayer',
            description: 'Build with us',
          },
          {
            title: 'Join the Discord',
            description: 'Talk with us',
          },
          {
            title: 'Follow on X',
            description: 'Engage with us',
          },
          {
            title: 'Connect on LinkedIn',
            description: 'Connect with us',
          },
          {
            title: 'Join our Telegram',
            description: 'Chat with us',
          },
          {
            title: 'Stay up to date in Medium',
            description: 'Learn with us',
          },
        ],
      },
      investors: {
        ourInvestors: 'Our Investors',
        investors: 'Investors',
        'Co-lead': 'CO-LEAD',
      },
      ecosystem: {
        ecosystem: 'Ecosystem',
        top: 'Top',
        Dapps: 'Dapps',
        on: 'on',
        Bitlayer: 'Bitlayer',
        'Dapp Center': 'Dapp Center',
        lending: 'Lending',
        dex: 'DEX',
        staking: 'Staking',
        gaming: 'Gaming',
        'stable coins': 'Stable coins',
      },
    },
    aiGrant: {
      title: '<span>Reimagine Bitcoin</span> + AI',
      bootcamp: 'Bitlayer AI Boostcamp',
      docs: 'Docs',
      contact: 'Contact Us',
      buildOnBitlayer: 'Build your <span>AI</span> program on Bitlayer',
      empowerWithMCP: 'Empower Your AI program with MCP',
      partner: 'Partner',
      booster: 'Booster',
      boosterHint:
        'If you have any idea about AI agent, Please contact us. We will do our best to help you achieve it.',
    },
    developers: {
      title: {
        titleLine1: 'Welcome to',
        titleLine2: 'Bitlayer Docs',
        subtitle: 'A manual for joining the Bitlayer ecosystem By builders for builders',
        buildNow: 'Build Now',
        blog: 'Blog',
      },
      faq: {
        title: 'FAQ',
        name1: 'What is Bitlayer?',
        desc1:
          'Bitlayer acts as a Layer 2 solution for Bitcoin, boasting 100% EVM and Ethereum toolchain compatibility, with BTC as native token(gas token). Bitlayer can enable applications and developers from the existing Ethereum ecosystem to migrate to Bitlayer at low cost, eliminating the need for substantial modifications or rewrites.',
        name2: 'Is Bitlayer compatible with existing Bitcoin wallets?',
        desc2:
          'Yes, Bitlayer is compatible with existing wallets such as Metamask, Unisat, or other Bitcoin/Ethereum-compatible wallets, allowing users to seamlessly interact with their funds and assets on the Bitlayer network.',
        name3: 'Can developers migrate their existing projects to Bitlayer?',
        desc3:
          'Yes, Bitlayer supports existing developers by offering EVM compatibility, allowing for seamless migration and deployment of existing projects at low costs. Developers can have the ease of migrating smart contracts written in Solidity, Vyper, or any other language that compiles EVM bytecode directly to Bitlayer, using the toolchain you are familiar with: Ethereum JSON-RPC, Hardhat, etc.',
        name4: 'How can I help support Bitlayer?',
        desc4: `There are several ways to support Bitlayer. You can actively participate in community discussions, provide feedback and suggestions, contribute to the development of applications or tools on the platform, or promote Bitlayer to others who may benefit from its services. Additionally, you can explore any specific support initiatives or programs that Bitlayer may have in place. Click to know more about <span data-link-index='0' class='bl-underline bl-cursor-pointer'>Ready Player One</span>, a program unlocks $50,000,000 in incentives for early builders and contributors.`,
      },
      feature: {
        title: 'Our Strength',
        title1: 'Turing-Completeness',
        desc1: 'Supports multiple VMs, enables a 100% EVM-Compatible Environment',
        title2: 'Trustless 2-Way Peg',
        desc2:
          'Combining DLC & BitVM, bringing groundbreaking models that supersede traditional multisig',
        title3: 'Layer 1 Verification',
        desc3: 'Inherits Bitcoin security via BitVM',
      },
      quickstart: {
        title: 'Developer Quickstart',
        subtitle: 'Unlock the Bitlayer ecosystem with a guide crafted by builders, for builders',
        title1: 'Connect Your Wallet to Bitlayer Testnet',
        skill1:
          'Add the Bitlayer Testnet configurations to your wallet and interact with the Dapps on Bitlayer Testnet',
        title2: 'Compile, Run and Deploy',
        skill2: 'This guide walks you through compiling and running Bitlayer to deployment',
        title3: 'Fresh Perspectives and Latest Updates Await',
        skill3: 'Stay tuned for detailed updates on technology and market insights in our blog',
        readMore: 'READ MORE',
      },
      intro: {
        title: 'TRACK PACK',
        subtitle:
          "A Builder's Guide to Joining the Bitlayer Ecosystem: Created by Builders, for Builders.",
        buildNow: 'Build Now',
      },
      tools: {
        title: 'Boost Tools',
        subtitle:
          'Leverage the suite of tools within the Bitlayer ecosystem to maximize your potential on Bitlayer',
        name1: 'Faucet',
        desc1: 'Obtain your Bitlayer Testnet tokens every 24 hours for development here.',
        name2: 'Mainnet Scan',
        desc2: `A essential tool for exploring and analyzing blockchain data on Bitlayer Mainnet.`,
        name3: 'Multisig Wallet',
        desc3: "Stealth your asset's security with decentralized multiple signers.",
        name4: 'The Graph',
        desc4: 'Index and access real-time blockchain data on Bitlayer.',
        website: 'Website',
        more: 'Explore More Tools',
      },
      security: {
        title: 'Security',
        subtitle: 'Use security suites to quickly build secure and reliable applications',
        name1: 'Dapp Security Manual',
        desc1: `Enhance the development efficiency of your application's security modules by following the security manual.`,
        buttonLabel1: 'Read doc',
        // name2: 'Audit alliance',
        name2: 'Security Network',
        desc2: `Quickly connect with renowned security service providers to safeguard and enhance the value of your application.`,
        buttonLabel2: 'Read doc',
        name3: 'Opensource Tools',
        desc3: 'Quickly perform self-detection using open-source security tools.',
        buttonLabel3: 'Explore it',
      },
      operationalSupport: {
        title: 'Operation Supports',
        subtitle: 'Grant, funding, incentives, and supports',
        name1: 'Ready Player Grant',
        desc1:
          "Unlock Bitlayer's Potential: Seeking Innovative Projects and Exceptional Teams to Advance the BTC Ecosystem.",
        name2: 'Incentive Program',
        desc2: `Ready Player I——$50,000,000 in incentives for early builders and contributors.`,
        name3: 'Ops & MKT Source',
        desc3:
          'Official resources support & Global market resources support. Leaderboard, DApp Center, Racer Center.',
        name4: 'Eco Growth Campaign',
        desc4: 'Mining Gala, The Voice of Bitlayer, Global Crypto Conference.',
        participate: 'Participate',
        more: 'More Supports',
      },
      cards: {
        name1: 'Quick Start',
        desc1:
          "Bitlayer is the first Layer 2 solution for Bitcoin that offers security equivalent to Bitcoin's own and Turing completeness. Learn how to build dApps on it.",
        name2: 'Bitlayer Architecture',
        desc2:
          'Get started with Bitlayer Network and understand its unique features. Learn about the networks architecture and how it differs from all the other blockchains.',
        name3: 'Bitlayer Roadmap',
        desc3: `The vision of Bitlayer will be realized through the rollout of a mainnet in multiple stages, with each phase designed to enhance the user experience.`,
        readDoc: 'Read Doc',
      },
      connect: {
        title: 'Connect with Us',
        subtitle:
          'Stay informed about the most recent news and advancements within the Bitlayer Community',
      },
    },
    bridge: {
      // transfer
      bridgeTitle: 'Bridge & Earn',
      gasTitle: 'Get Bitlayer Gas',
      gasPromotion: 'Quick Start - Get Bitlayer Gas in 1 Minute!',
      newToBridge: 'New to bridge?',
      from: 'From',
      to: 'To',
      amount: 'Amount',
      balance: 'Balance: {{balance}}',
      available: 'Available: {{balance}}',
      transferable: 'Transferable inscriptions',
      max: 'Max',
      recipientAddress: 'Recipient Address',
      recipientTip: 'Connect wallet to receive tokens',
      est: 'Est: {{time}}',
      fee: 'Fee:',
      total: 'Total:',
      receivepPlaceholder: 'Please enter the {{chainName}} address',
      transfer: 'Transfer',
      transferProgress: 'Transfer is in progress',
      approve: 'Approve',
      approveInProgress: 'Waiting for your approval',
      checkingAllowance: 'Checking allowance',
      minLimit: 'Minimum {{amount}} {{symbol}} per transaction',
      maxLimit: 'Maximum {{amount}} {{symbol}} per transaction',
      invalidAmount: 'Invalid amount',
      invalidAddress: 'Invalid address',
      switchChainDesc:
        'Switching to the {{chainName}} network requires the use of {{networkType}} wallet.',
      transferFailed: 'Transfer Failed',
      amountExceed: 'Amount exceeds the limit',
      connectDesc: 'Connect your wallet to transfer tokens',
      bridgeTip: 'Bridge to Bitlayer, Hold Bitlayer Lucky Helmet, Share 400K BWB Points!',
      historyTab: 'History',
      getGas: 'Get Gas Express',
      swap: 'Swap',
      gasLimit: `Balance ≥{{maxBalance}} can't be swaped`,
      dayLimit: `Today's remaining BTC is {{remainSwapAmount}}`,
      // history
      loadingData: 'Loading Data',
      noData: 'No Data',
      sender: 'sender',
      receiver: 'receiver',
      transactionHash: 'Transaction Hash',
      Pay: 'You pay',
      gasPay: 'Pay',
      gasGet: 'Get',
      Balance: 'Balance:',
      Max: 'Max',
      SwitchToBitlayer: 'Switch To Bitlayer',
      Maximum: 'Maximum {{maxSwapAmount}} BTC per transaction',
      History: 'History',
      Receive: 'You receive',
      Fee: 'Fee:',
      Total: 'Total:',
      Slippage: 'Slippage:',
      ThirdPartyBridge: 'Third Party Bridge',
      ConfirmSwap: 'Confirm Swap',
      ApproveToken: 'Approve token',
      ApproveGas: 'Approve signature without paying gas',
      ConfirmSwapRadio: 'Confirm swap radio',
      ConfirmGas: 'Confirm swap radio without paying gas',
      Processing: 'Processing...',
      Completed: 'Completed',
      From: 'From',
      To: 'To',
      Return: 'Return',
      thirdParty: {
        tipTitle: 'Kindly Reminder',
        tipContent:
          'The current Bitlayer EVM bridge is a beta version. Please use third-party bridge service for small amount bridge.',
      },
      btcHint: 'Only supports Identify BTC, Ordinal and Runes protocol assets',
      btcCaution: {
        title: 'Confirm Transaction',
        message:
          'This transaction will use the following Inputs. Please confirm that these Inputs do not carry other assets.',
      },
      inscribeTransfer: 'Inscribe TRANSFER',
      inscribedTitle: 'Transaction Sent',
      inscribedTip: 'Inscribe will be initiated after confirmation.',
      viewOnExplorer: 'View on Explorer',
      refresh: 'Refresh',
      pending: 'Pending',
      selectAll: 'Select All',
      maintenanceTip: 'Maintenance details',
      underMaintenance: 'Under maintenance, stay tuned',
      usdc: {
        change: 'USDC Change',
        changeLabel: 'Your token changes:',
        changeDesc:
          "This token was updated based on the Circle's native smart contract. New USDC contract address:",
        confirm: 'Confirm & Reward',
        learnMore: 'View More Details',
        bonusForYou: 'Bitlayer Bonus Points for U !',
        tips: 'Kind Tips',
        lendingDesc:
          'We detected that you have USDC loans on the lending platform. Please repay them first.',
        borrowingTitle: 'USDC.e borrowing situation',
        viewOn: 'View on {{ platform }}',
        insufficentBalance: 'Insufficient USDC balance. Please contact us.',
      },
      quickJump: {
        jump: 'Jump',
        tips: 'You will jump the maximum amount each time',
        claim: 'Claim',
        title: 'Quick Jump to BSC',
        step1: 'Step 1:<span>Jump Out</span>',
        step2: 'Step 2:<span>Claim On BSC</span>',
        step3: 'Step 3:<span>Gas Return</span>',
        note: '*Quick jump is a temporary cross-chain service designed to serve users participating in Binance Carnival, specifically for certain tokens involved, offering gas subsidies on Bitlayer.',
        noteImportant: 'Offering jump-out gas fee return on Bitlayer.',
      },
      errors: {
        invalidRequest: 'Invalid request.',
        insufficentBtc:
          'Insufficient BTC balance or no available UTXO. If you have unconfirmed transactions, please wait for the transaction to be confirmed and try again.',
        insufficentRune:
          'Insufficient Rune balance or no available UTXO. If you have unconfirmed transactions, please wait for the transaction to be confirmed and try again.',
        insufficentBrc20:
          'Insufficient BRC20 balance or no available UTXO. If you have unconfirmed transactions, please wait for the transaction to be confirmed and try again.',
        buildTxFailed: 'Failed to build transaction.',
        transferError: 'Failed to transfer.',
        internalError: 'Internal service error.',
        swapError: 'Failed to swap.',
        insufficientFunds:
          'Insufficient funds: The total cost (gas * gas fee + value) of executing this transaction exceeds the balance of the account.',
        insufficientGas: 'Insufficient gas for this transaction',
        withdrawLimitReached:
          'Maximum withdrawal: {{ limit }} {{ token }}; Currently, {{ available}} {{ token }} is available for bridge; Recovery rate: {{ recovery }} {{ token }} per second.',
      },
      inscribeProvider: {
        inputLabel: 'Inscribe Amount',
      },
    },
    faucet: {
      title: 'Testnet Faucet',
      description:
        'Obtain Bitlayer Testnet tokens every 24 hours for development. Testnet token has no financial value and cannot be traded at a real price.',
      selectField: {
        label: 'Select Token',
        placeholder: 'Select a token',
      },
      textFiled: {
        label: 'Wallet Address',
        placeholder: 'Enter your Bitlayer Testnet address',
      },
      result: {
        gotTip: 'You got {{tokens}}!',
        gotAnother: 'You can request for another {{token}} in 24 hours.',
        sending: 'Sending...',
      },
      error: {
        testAddress: 'Please enter your bitlayer testnet address',
        verifyCaptcha: 'Please complete the captcha verification',
        verifyFailed: 'Failed to verify captcha',
        exceededLimit:
          'You have requested tokens in the last 24 hours. Please wait {{h}}{{m}}{{s}} before trying again.',
        invalidAddress: 'Please enter a valid bitlayer testnet address',
        unexpectedError: 'An unexpected server error occurred. Please try again later.',
      },
    },
    readyplayone: {
      title: 'Ready Player One',
      description:
        'Unlock <text>$50,000,000</text> in incentives for early builders and contributors.',
      register: 'Register',
      time: 'Mar. 29, 2024 - May. 10, 2024',
      airdrop: 'Up to $1M Incentives For Each Project!',
      Volume: 'Volume:',
      rules: {
        title: 'Rules',
        ruleContents: [
          {
            title: 'Register',
            texts: ['Ensure registration is completed by May 10th.'],
            date: 'Mar-Apr',
          },
          {
            title: 'Launch',
            texts: ['Initiate launch on the mainnet and gear up for the impending competition.'],
            date: 'Apr-May',
          },
          {
            title: 'Airdrop',
            texts: [
              'Join the leaderboard competition to win a token airdrop worth over $50 million.',
            ],
            date: 'Jun-Jul',
          },
        ],
      },
      colead: 'Co-Leads',
      investors: 'Investors',
      partners: 'Partners',
      setp: {
        register: {
          schemaName: 'Please provide the project name',
          schemaWebsite: 'Please provide the project website',
          schemaProjectDemo: 'Please provide the project demo',
          schemaContact: 'Please provide the contact name',
          schemaEmail: 'Please provide the project email',
          schemaTelegram: 'Please provide the project telegram',
          schemaAgree: 'Please agree to the terms and conditions',
          newBuilderType: 'New Builder',
          experiencedBuilderType: 'Experienced Builder',
          formValidError:
            'We found some issues in your form. Please review the highlighted fields and make corrections before resubmitting.',
          apiClientError:
            'We found some issues in your form. Please review the fields and try to submit again. ({{code}})',
          apiServerError:
            'There seems to be a temporary glitch on our end. Please refresh the page or try again in a few minutes. ({{code}})',
          formOne: {
            title: 'Project Information',
            name: {
              label: 'Project name',
              placeholder: 'Please enter the name',
            },
            builderType: {
              label: 'Builder Type',
              placeholder: 'Please select the builder type',
            },
            fundInfo: {
              label:
                "Project Fund Info:(Describe the project's funding status. in 500 characters or provide a detail link.)",
              placeholder: 'Please enter the fund info',
            },
            projectDemo: {
              label: 'Project Demo',
              placeholder: 'Please enter the demo link',
            },
            pitchDeck: {
              label: 'Project Pitch Deck(Only support pdf format)',
              placeholder: 'Select file to upload',
            },
            twitter: {
              label: 'Project twitter',
              placeholder: 'Please enter the id',
            },
            website: {
              label: 'Project website',
              placeholder: 'Please enter the URL',
            },
            logo: {
              label: 'Project logo <span>(1 by 1 ratio recommended)</span>',
              placeholder: 'Select file to upload',
            },
            description: {
              label:
                'Project Description/Deck: Summarize your project (purpose, problem solved, RoadMap ,how it works) in 500 characters or provide a deck link.',
              placeholder: 'Please enter the description',
            },
            category: {
              label: 'What is the category of your project?',
              placeholder: 'Please select',
              selects: [
                'Infra',
                'Defi',
                'Social',
                'Inscriptions',
                'NFT',
                'MeMe Token',
                'DAO',
                'Tooling',
                'Wallet',
                'Cross-chain',
                'Privacy computering',
                'Privacy Token',
                'Prediction makert',
                'Storage',
                'Gamefi',
                'Entertainment',
                'Metaverse',
                'Others',
              ],
            },
            stage: {
              label: 'Project stage',
              placeholder: 'Please select',
              selects: ['Idea phase', 'In development', 'Prototype available', 'Live/Beta'],
            },
            coin: {
              label:
                'Token addresses on Bitlayer <span>(if not available, please fill in N/A)</span>',
              placeholder: 'Please enter the token addresses',
            },
            lockUp: {
              label:
                'Lock-up addresses on Bitlayer <span>(if not available, please fill in N/A)</span>',
              placeholder: 'Please enter lock up addresses',
            },
            contract: {
              label:
                'Dapp contract addresses on Bitlayer <span>(if not available, please fill in N/A)</span>',
              placeholder: 'Please enter the contract addresses',
            },
            defilama: {
              label: 'Project Defillama link <span>(if not available, please fill in N/A)</span>',
              placeholder: 'Please enter the defillama link',
            },
          },
          formTwo: {
            title: 'Team Information',
            team: {
              label:
                'How many are there? List the names, roles and  backgrounds of main team members.',
              placeholder: 'Please enter the description',
            },
          },
          formThree: {
            title: 'Contact Person',
            name: {
              label: 'Name',
              placeholder: 'Please enter the name',
            },
            email: {
              label: 'Email address',
              placeholder: 'Please enter the email address',
            },
            telegram: {
              label: 'Telegram',
              placeholder: 'Please enter the url',
            },
            twitter: {
              label: 'Twitter profile',
              placeholder: 'Please enter the url',
            },
          },
          consent: {
            title: 'Consent',
            label:
              "I agree with the hackathon's terms and conditions, privacy policy, and code of conduct.",
          },
        },
        launch: {
          title: 'Congratulations on successfully registering!',
          documents: 'Developers documents',
          deploy: 'Now you can deploy your project on bitlayer!',
          scanCode: 'Scan to join the community',
          next: 'Next',
        },
        airdrop: {
          deploymentTip: 'If your project deployment completes, well done! ',
          description:
            'You will be able to join the primary election! Final winners will receive grants between $10K to $300K. Additional bonuses include honorary rewards of $3K to $5K and access to a $1M prize pool for running events!',
        },
      },
      gems: {
        title: 'Bitlayer Gems Airdrop',
        gems: 'Gems',
        tips: ' will be distributed soon!',
        button: 'Check my eligibility',
        claimGems: {
          button: 'Claim Gems',
          label1: 'You are eligible for your ',
          label2: 'Gems airdrop!',
          'failed-title': 'Keep Going!',
          'label-failed':
            'You are not eligible for gems airdrop in this round. Stay tuned for more events later!',
          'button-failed': 'Earn Rewards from Dapps',
        },
      },
      subTitle: 'Bitlayer Booster Grants Program',
      descriptionV4:
        'Fair And Transparent, Monthly Review, Up to <text> $1Million </text>  Incentives For Each Project!',
      incentives: {
        tag: 'Incentives',
        'New Builder': {
          title: 'New Builder',
          bonus: 'Honorary titles & rewards of <span>{{amount}}</span>',
        },
        'Experienced builder': {
          title: 'Experienced Builder',
          bonus: '<span>{{amount}}</span> exclusive event prize pool',
        },
        grants: 'Grants',
        bonus: 'Bonus',
      },
      rulesV4: {
        tag: 'Rules',
        ruleContents: [
          {
            title: 'Registration',
            texts: [
              'Projects registered before the 30th of each month will be evaluated in the current month',
              'Projects registered after the 30th will be evaluated in the following month',
              'Ensure the sufficient information is provided in registration',
            ],
            date: 'Monthly',
          },
          {
            title: 'Primary Election',
            texts: [
              'The Bitlayer review committee will conduct rolling review based on the submitted information.',
              'Further review may included technical checks, market research, and product analysis.',
            ],
            date: 'Est. 7 days',
          },
          {
            title: 'Final Election',
            texts: [
              'Bitlayer Foundation, alongside with an investment institution judge will evaluate projects passed the primary round.',
              'Each project will make a 10-15 minutes presentation',
              'Winning projects will receive $10,000 to $300,000 grants',
            ],
            date: 'Est. 14 days',
          },
        ],
      },
      currentShortlist: {
        tag: 'Current Shortlist',
        primaryElection: 'Primary Election',
        finalElection: 'Final List',
        like: 'Like',
        grant: 'Grant',
        grantWinner: 'Grant Winner',
      },
      updates: {
        tag: 'Updates',
      },
    },
    luckyhelmet: {
      title: 'Lucky Helmet',
      description: 'Be a driver. Earn from Bitlayer.',
      toGet: 'How to get?',
      miningBtn: 'Minting Opens 12:00pm UTC on May 8th',
      assetTitle: 'Asset on Layer 1',
      assetDesc: 'Native Bitcoin Ordinals Asset on Bitcoin Layer 1',
      mintTitle: 'Mint on Layer 2',
      mintDesc: 'Lower gas fees and better efficiency when compared to layer 1',
      desc1: 'Seamless L1/L2 Experience',
      desc2: 'Golden Shovel Bitlayer Perks',
      minting: 'Minting',
      mint: 'Mint',
      checkWhitelist: 'Check Whitelist',
      minted: 'Minted',
      mintingSuccess: 'Minting Success',
      congratulations: 'Congratulations!',
      success: 'You are on the whitelist.',
      sorry: 'Sorry',
      failure: 'You are not on the whitelist.',
      finished: 'Finished',
      whitelistOnly: 'Whitelist Only',
      trade: 'Trade Lucky Helmet',
    },
    rank: {
      head: {
        head1: 'Award Pool',
        head2: 'Dapps',
        head3: 'Total Votes',
        head4: 'Daily Votes',
      },
      banner: {
        tag: 'Preparation Stage',
        title: 'READY PLAYER ONE',
        subtitle: 'token for builders',
        link: ' Get To Know Our Rules?',
        all: 'All categories',
        switch: 'Switch to',
        register: ' Register Dapp',
      },
      list: {
        votes: 'VOTES',
        vote: 'Vote',
        out: 'Out of votes',
        invite: 'Invite for Points',
      },
      carouse: {
        title: 'My Ready Player I Points',
        note: 'Please note: Ready Player I Points are not equivalent to Bitlayer Points. They can be exchanged at a certain rate in the future. Follow us for updates!',
        accepted: 'Invite Accepted',
        taskCenter: 'Task Center',
        now: 'Follow Now',
        vote: 'Every Vote',
        invite: 'Every Invite',
        follow: 'Follow on X',
        hint1: 'Up To 3 Times A Day',
        hint2: 'One Time Task',
      },
      dialog: {
        band: {
          title: 'Invite Friends to Boost',
          sorry:
            'Sorry! Your Twitter is already linked to a different address. Please use another Twitter account',
        },
        invite: {
          need: 'You need to bind an X (Twitter) account first! Earn 500 Points for each successful invite now!',
          auth: 'Authorize on X',
          attention:
            'Attention: One Twitter account can only be linked to one address to avoid errors',
        },
        note: {
          please: 'Please note:',
          note: 'Ready Player I Points are not equivalent to Bitlayer Points. They can be exchanged at a certain rate in the future. Follow us for updates!',
        },
        rules: {
          rules1: 'Earn 300 points daily by completing three votes.',
          rules2: 'Follow the official Twitter to receive 500 points.',
          rules3:
            'Each new user invited to vote successfully adds an extra 500 points to your tally.',
          rules4:
            'A random draw will award 100 Bitlayer Lucky Helmets, with higher rankings increasing your chances.',
          vote: 'Voting for Your Favorite Projects',
          note: 'Join the Bitlayer Popularity List voting to support your favorite projects! Event period: April 23, 2024, to May 10, 2024. Log in daily to the leaderboard page, with each person having three voting opportunities per day. Earn extra points by following the official Twitter and inviting new users. Active participants will accumulate Ready Player I event points and have the chance to be whitelisted for the Bitlayer Lucky Helmet.',
          rewards: 'Rewards:',
          boost: `Boost your project's popularity, influence the final leaderboard rankings, and actively participate to win official NFT rewards!`,
          gotIt: 'Got It',
        },
        share: {
          copy: 'Copy Link',
          share: 'Share On',
          invite: 'Invite to vote',
          more: 'Get more votes from friends',
          connect: 'Connect X (Twitter)',
        },
        vote: {
          vote: 'Vote',
          voteFor: 'Vote for',
          daily: '3 votes daily',
          votes: 'Votes',
        },
        expire: {
          late: 'Sorry you are late!',
          expired: 'The voting has ended.',
          tuned: 'More details will be shared on our official twitter, please stay tuned!',
          thx: 'Thank You!',
          follow: 'Follow Us Now',
        },
      },
    },
    getGas: {
      recipient: 'Recipient Address',
      placeholder: 'Please enter a Bitlayer address',
      invalid: 'Invalid address',
      purchase: 'Purchase',
      maintain: 'The system is upgrading, please try again later',
      maintainMobile: 'The system is upgrading',
      announcement: 'Security upgrade for 4 hours,please go bridge',
      time: 'Estimated time: About 1 minute after payment completion',
      getGas: 'Get Bitlayer Gas Express',
      getRegular: 'Get Gas Regular',
      after: 'After transferring，you will be able to view the orders',
      history: 'History',
      payment: 'Payment Address',
      timeout: 'QR Code Timeout',
      warning: 'Don&apos;t transfer to an expired address, or you may risk losing tokens',
      amount: 'Payment Amount:',
      cancelWarning:
        'If the order is cancelled, please do not transfer any tokens to the payment address, as it may result in a loss of your funds.',
      ok: 'OK',
      cancel: 'Cancel Order',
      Complete: 'Complete',
      getBitlayerGas:
        'You could get Bitlayer Gas by transferring tokens to the payment address from any supported wallet/exchange.',
      promotionNote1: 'Promotion: only',
      fee: ' $1 ',

      promotionNote2: 'for the Get Gas Express fee, right now!',
      addressNotion:
        'Confirm the blockchain, token, and amount accurately. Each payment address can only be used once. Failure to do so may result in the loss of your deposited funds.',
      promotion: 'Fee Promotion:',
      estimated: 'Estimated time: ~1 minute',
      ensure: 'Please ensure the following',
      content: `Confirm The <strong>Blockchain, Token, And Amount</strong> Accurately. Each Payment
                Address Can Only Be <strong>Used Once.</strong>
                <div>Failure To Do So May Result In The Loss Of Your Deposited Funds.</div>`,
      aboutFlash: 'About Flash Bridge-In',
      aboutInfo: `a. All funds from Flash Bridge-In are transferred through Bitlayer official bridge.<br>b. Designed for users to access Bitlayer faster and more cost-effectively.`,
      get: 'Get',
      right: 'Get them right !',
      otherwise: 'Otherwise,',
      lose: 'you may lose your assets',
      Blockchain: 'Blockchain',
      Amount: ' Amount',
      token: 'Token',
      pay: '1 address for 1 pay',
      kind: 'Kind tips',
      hasBridge:
        'You are about to access a third party link, which does not belong to Bitlayer. Your behavior in the third party link is subject to the Privacy Policy and User Agreement of the third party link, and the third party is solely responsible for you. Please understand the relevant situation and consequences.',
      notBridge:
        'This is an exclusive link for PEPE events. You can visit it to claim your rewards after completing a bridge transaction.',
      commonRisk: 'Common risks or scams',
      phishing: 'Phishing site',
      authorization: 'Authorization risk',
      highYield: 'High-yield scam',
      ponzischeme: 'Ponzi scheme',
      freeAirdrop: 'Free" airdrop',
      contractLoophole: 'Contract loophole',
      aware: "I'm aware of the risks",
    },
    leaderBoard: {
      title: 'Bitlayer Leaderboard Competition Epoch 1',
      support: 'Listing Support',
      period: 'Data statistics period:',
      incentives: 'Incentives',
      liquidity: 'Liquidity Support',
      forPartners: 'For Eco-Partners',
      rules: 'Get To Know Our Rules?',
      topTvl: 'Top TVL',
      topTransation24: 'Top Transactions',
      topTransaction: 'Top Transaction',
      topPopularity: 'Top Popularity',
      viewAll: 'View All',
      tryNow: 'Try Now',
      countdown: 'Epoch Countdown',
      countEnded: 'Epoch ended',
      stage3: 'Stage 3 Competiton Epoch',
      stage2: 'Stage 2 Preparation Stage',
      gemsMinted: 'Total Gems Minted',
      locked: 'Total Value Locked',
      transactionCount: 'Transaction Count',
      totalLikes: 'Total Likes',
      tvlRanking: 'TVL Ranking List On Bitlayer Mainnet Ecosystem',
      discoverTvl: 'Discover The DApps Built On Bitlayer Mainnet With The Top TVL',
      txnRanking: 'TXN Ranking List On Bitlayer Mainnet Ecosystem',
      discoverTransactions:
        'Discover the DApps Built On Bitlayer Mainnet With The Most Transactions',
      rankingList: 'Popularity Ranking List On Bitlayer Mainnet Ecosystem',
      discoverPopular: 'Discover The Most Popular DApps Built On Bitlayer Mainnet',
      top10: 'Top10 Leaderboard',
      tvl: 'TVL',
      transactions: 'Transactions',
      likes: 'Likes',
      valueUpdate:
        'The total value of effective assets locked in the DApp protocols on the Bitlayer Mainnet, daily update.',
      numberUpdate:
        'The number of effective transactions made by users calling the Dapp contract, daily update.',
      likesUpdate:
        'The accumulated likes of the project during the competition period, real-time update.',
      name: 'Name',
      category: 'Category',
      gemMinted: 'Gems Minted',
      gemsPoints:
        'Gems are calculated based on Total Value Locked (TVL), number of effective transactions, active users amount, Bitlayer popularity leaderboard performance, and product strength. The data will be updated at 00:00 (UTC) every day.',
      boost: 'Boost',
      bindX: 'Bind Your X (Twitter)',
      bindNeed: 'You need to bind an X (Twitter) account first!',
      start: 'Start in May 23rd',
      airdrop: 'Airdrop In Progress!',
      reward: 'Reward',
      rank: 'DApp Ranking',
      dappCenter: 'DApp Center',
      more: 'More',
      introduce: 'Activity Introduction',
      gems: '100% Of Gems',
      distributed: 'Will Be Distributed To Users!',
      rewards: 'Rewards',
      completed: 'Completed',
      winnerList: 'Winner list',
      slots: 'winners',
      win: 'Complete to Win',
      startAt: 'Start at',
      eventCountdown: 'Event countdown',
      ended: 'Finished',
      verify: 'verify',
      verified: 'verified',
      tutorial: 'Tutorial',
      participated: 'Participating',
      tasks: 'Tasks',
      congratulation: 'Congratulations!',
      foru: 'Bitlayer Bonus Points for U !',
      export: 'Explore Bitlayer dApps and earn more rewards!',
      phase: 'season {{num}}',
      'Free Drop': 'Free Drop',
      '$1 Sweeptake': '$1 Sweeptake',
    },
    dappDetail: {
      reward: 'Reward:',
      activity: 'Activity',
      join: 'Join Now！',
      introduce: 'Activity Introduction',
      whatIs: 'What is {{name}} ?',
      overView: 'Overview',
      dappIntro: 'Introducing Our DApp!',
      event: `Here's How You Can Join the Airdrop Event:`,
      team: 'Team',
      twitterText:
        'Join with me and try {{name}} on @BitlayerLabs now! Seize the rewards from 100+ #Bitlayer ecosystem dapps!',
      shareTitle: 'Share with friends',
      shareText:
        'Share and try your favorite projects with your friends now! Be the early birds to seize potential rewards!',
    },
    miningGala: {
      meta: {
        title: 'Win bitlayer rewards & DApp airdrops',
      },
      head: {
        time: 'May 27th, 2024 13PM UTC - June 10th, 2024 13PM UTC',
        doubleGain: 'Double Gain',
      },
      miningPolls: {
        title: 'Mining Pools',
        claim: 'Claim',
        minted: 'Minted',
        taskTitle: 'Complete Tasks to Mint a Pioneer Badge',
        taskTip: 'Complete at least one transaction with {{project}}',
        shareText: 'Share with your firends',
        or: 'Or',
        hasHelmet: 'Hold Lucky Helmet',
        auditReport: 'audit report',
        miningGuide: 'mining guide',
        airdrop: 'Airdrop',
        detailRules: 'Detail Rules',
        claimSuccess: 'Claim Success!',
        claimFailed: 'Claim failed, Please try again later.',
        bitlayerTask: 'Participate the Bitlayer Mining Gala',

        lorenze: {
          desc: 'The premier platform for Bitcoin liquid restaking token issuance, trading, and settlement through Babylon.',
          tasks: [
            'Stake BTC to the Babylon Pre-launch Staking and get stBTC.',
            'Bridge the stBTC received to Bitlayer Mainnet.',
            'Participate in the Bitlayer DeFi ecosystem with your stBTC.',
          ],
        },
        bitsmiley: {
          desc: 'BTC-Native stablecoin protocol, Initially funded by OKX Ventures & ABCDELabs.',
          tasks: [
            'Mint bitUSD on bitsmiley.',
            'Add liquidity to bitUSD-USDT / bitUSD-WBTC on bitCow.',
            'Stake bitUSD on Defi protocols.',
          ],
        },
        avalon: {
          desc: 'Avalon Finance strives to become the best decentralized lending protocol on BTC layer 2.',
          tasks: [
            'Supply assets on Bitlayer, earn at least 1000 supply points.',
            'Borrow assets on Bitlayer, earn at least 500 borrow points.',
            'Loop your supply and borrowing on Avalon Finance.',
          ],
        },
        bitcow: {
          desc: 'BTC-Native stable and concentrated liquidity AMM.',
          tasks: [
            'Add liquidity to bitUSD-WBTC / bitUSD-USDT trading pairs.',
            'Participate in trading bitUSD-USDT or other pairs.',
            'Create new trading pairs and increase the trading volume.',
          ],
        },
        pell: {
          desc: 'Pell is a decentralized trust marketplace that diversifies BTC and LSD yield, aiming to enhance security for BTC L2 networks.',
          tasks: [
            'Connect your wallet to sign in.',
            'Stake over 0.001 BTC and keep it locked for 7 days.',
            'Lucky draw: 1.5x permanent points card x 1000, 100 USDT x 10.',
          ],
        },
        enzo: {
          desc: 'Enzo Finance is a best-decentralized lending protocol on the BitLayer chain with its cutting-edge algorithmic.',
          tasks: [
            'Deposit / Stake your Bitlayer assets on Enzo Finance.',
            'Borrow Bitlayer assets on Enzo Finance.',
            'Daily 1BTC Giveaway (Requirements: total deposits / loans > $100 USDT).',
          ],
        },
        bitparty: {
          desc: 'BitParty is the first "asset gamified community network" in the BTC ecosystem!',
          tasks: [
            'Obtain Bitlayer assets through the cross-chain bridge.',
            'Stake your Bitlayer assets in Bitparty and earn points to get $BTPX.',
            'Join the groups and seize territory with the others!',
          ],
        },
      },
      shareDialog: {
        title: 'Share with friends',
        desc: 'Share with your friends, participate in Bitlayer Mining Gala, and collect exclusive Bitlayer Mining Gala Pioneer Badges Now!',
      },
      tipDialog: {
        title: 'Kind Tips',
        desc: 'insufficient BTC banlance，you can replenish BTC in the following ways.',
        depositBtn: 'Lightning deposit',
        bridgeBtn: 'Bridge',
      },
      twitterShare: {
        projectShare: `Join with me, participate in {{name}} mining pool on @BitlayerLabs #MiningGala now! {{money}} airdrop waiting for you to share!\n`,
        badgeShare:
          'I just minted my @BitlayerLabs #MiningGala Pioneer Badge here! Enter the page to free mint yours! Join with me to share $24,350,000 airdrops!\n',
      },
      bottomTip: {
        title: 'Useful Tutorials for Bitlayer Mining Gala!',
        desc: 'Welcome to Bitlayer Mining Gala! Check our guidance below and enjoy your mining trip here!',
        tips: [
          'Get Bitlayer Gas in 1 minute.',
          'Tutorial for bridging assets to Bitlayer.',
          'The best way to join Bitlayer Mining Gala.',
          'Bitlayer Mining Gala Pioneer minting guidance.',
        ],
      },

      taskTo: 'Task To Get box',
      daily: 'Daily',
      twitter: 'Twitter',
      rewards: {
        title: 'rewards',
        boxes: 'Boxes',
        boxesTip: 'Each time completing the daily task',
        badge: 'Badge',
        total: 'Total',
        whitelist: 'Whitelist',
      },
      task: {
        title: 'Task',
        go: 'GO',
        verify: 'verify',
        daily: 'daily',
        deposit: 'Deposit',
        followX: 'Follow X',
      },
      poolList: {
        rollDex: {
          title: 'RollDex',
        },
      },
    },
    userCenter: {
      title: 'RACER CENTER',
      dailyTasks: 'DAILY TASKS',
      bitlayerPoints: 'BITLAYER POINTS',
      btr: 'BTR',
      bitlayerGems: 'BITLAYER GEMS',
      tasks: 'Tasks',
      badges: 'Badges',
      bitlayerDays: '<span>{{ days }}</span> DAYS IN BITLAYER',
      txn: 'TXN',
      bridged: 'Bridged',
      unlocked: 'Lv{{ level }} unlocked',
      locked: 'Lv{{ level }} locked',
      unlocking: 'Lv{{ level }} unlocking <span>{{ progress }}%</span>',
      tabs: {
        newRacerTasks: 'New Racer Tasks',
        advancedTasks: 'Advanced Tasks',
        ecoTasks: 'Eco Events',
        myBadges: 'My Badges',
        dailyTask: 'daily Tasks',
        farmingOnBitlayer: 'Farming on Bitlayer',
      },
      task: {
        complete: 'COMPLETE',
        claim: 'CLAIM',
        pointsAcquired: '<span>{{ points }}</span> Bitlayer Points Acquired.',
        check: 'Check',
      },
      claimGems: {
        title: 'Congrats! You have gems to be claimed.',
        action: 'Go to claim',
      },
      badge: {
        coming: 'COMING SOON',
        inProgress: 'IN PROGRESS',
        finished: 'FINISHED',
        claim: 'CLAIM',
        join: 'JOIN THE EVENT',
        owned: 'BADGE OWNED',
        notOwned: 'BADGE NOT OWNED',
        rewardCanClaim: 'can be claimed',
        rewardClaimed: 'claimed',
      },
      reward: {
        points: 'Points',
        gems: 'Gems',
        btr: 'BTR',
      },
      bybit: {
        canMint: 'You can mint a Bybit SBT',
        canNotmint: 'It seems that you could not mint Bybit SBT',
        hasMinted: 'You have minted a Bybit SBT',
        waiting: 'Waiting For On-Chain Confirm ~30s',
        mint: 'Mint',
        cancel: 'Cancel',
        minted: 'Minted',
      },
      goplus: {
        canMint: 'You can mint a Goplus SBT',
        canNotmint: 'It seems that you could not mint Goplus SBT',
        hasMinted: 'You have minted a Goplus SBT',
        waiting: 'Waiting For On-Chain Confirm ~30s',
        mint: 'Mint',
        cancel: 'Cancel',
        minted: 'Minted',
      },
      errors: {
        twitter: {
          accountBinded:
            'This Twitter account has been bound to another address. Please use another Twitter account.',
        },
        unexpectedError: 'An unexpected error occurred. Please try again later.',
      },
      rankTitle: 'MY Points Ranking',
      topRacers: 'Top Racers',
      racer: 'Racer',
      gains: 'Gains',
      invite: {
        discover: 'Discover bitlayer',
        and: 'and get',
        invite: 'Invite friends',
        refer: 'REFER FRIENDS',
        shareText:
          'Share your referral code/link to your friends, as they register bitlayer.org and send tokens on Bitlayer more than one time, you could get rewards.  (Update every 12 hours)',
        my: 'MY REFERRALS',
      },
      twitter: {
        bindTitle: 'Bind Your X (Twitter)',
        bindDesc: 'You need to bind an X (Twitter) account first!',
        bindAction: 'Authorize on X',
        tips: 'Attention: One Twitter account can only be linked to one address to avoid errors.',
      },
      draw: {
        history: {
          history: 'Draw History',
          time: ' Draw Time',
          reward: 'Reward',
        },
        error: 'You have 0 draw chance, invite friends to get more',
        up: 'Up to $10K in rewards',
        unit: 'Draw',
        invite: 'Invite for more chances and upgrade your lucky level !!',
        card: 'Draw A Card',
        wait: 'Waiting For On-Chain Confirm ~30s',
        warning: 'Warning',
        sorry: 'Sorry! 1000 Points At Least For Per Draw, You Don&apos;t Have Enough Points.',
        ok: 'OK',
      },
    },
    activities: {
      guessingGame: {
        title: 'Guess to earn',
        subtitle: 'Master of price prediction',
        olympics2024: {
          title: 'Guess to earn',
          subtitle: 'Olympics Paris 2024',
        },
        status: {
          inProgress: 'In progress',
          coming: 'Coming soon',
          waiting: 'Waiting',
          finished: 'Finished',
          releasing: 'Releasing result in 24 hours',
          endIn: 'End in',
        },
        placeBet: 'PLACE YOUR BET',
        bet: 'Bet',
        betUnit: '{{ step }} {{ rewardType }} per unit',
        betFor: '<icon /> <span>{{ amount }}</span> for {{ text }}',
        wonRewardFor: 'Won <icon /> <span>{{ amount }}</span> for {{ text }}',
        myBets: 'MY BETS',
        won: 'WON',
        return: 'Return',
        pool: 'Pool',
        reward: {
          claim: 'CLAIM',
          claimed: 'CLAIMED',
          claimAll: 'Claim All Rewards',
          congratulations: 'Congratulations!',
          claimedAllPoints: 'You earned <point /> <span>{{ points }}</span> from the racer center.',
          claimedAllGems: 'You earned <gem /> <span>{{ gems }}</span> from the racer center.',
          claimedAllBoth:
            'You earned <point /> <span>{{ points }}</span> and <gem /> <span>{{ gems }}</span> from the racer center.',
        },
        messages: {
          placedBet: 'You have successfully placed your bet. Good luck!',
          failedToPlaceBet: 'Failed to place bet. Please try again later.',
          claimedReward: 'You have successfully claimed your reward!',
          failedToClaim: 'Failed to claim reward. Please try again later.',
          insufficient: 'You need at least {{ amount }} {{ rewardType }} to place a bet.',
          alreadyClaimedAll: 'You have already claimed all rewards.',
          failedToClaimAll: 'Failed to claim all rewards. Please try again later.',
        },
      },
    },
    whiteList: {
      reward: 'My Rewards',
      address: 'Address',
      xp: 'Bitlayer points',
      lucky: 'Reward',
      points: 'points',
      winnerList: 'Winner List',
      description: `Notes: Users completing and verifying all tasks will be rewarded with Bitlayer Points within 7 days after the events ends. Project rewards will be distributed according to the project's official announcements. `,
    },
    bitvm: {
      title: {
        slogon1: 'BitVM Stack',
        slogon2: 'make Bitcoin',
        slogon3: 'verifiable',
        description: 'The missing Bitcoin scaling stack',
        whitePaper: 'Whitepaper',
        earlyAccess: 'Early Access',
        comingSoon: 'Coming Soon',
      },
      feature: {
        badge: 'Features',
        title1: 'Inherit Bitcoin Security',
        description1: 'With minimal engineering cost',
        title2: 'Modular & Composable',
        description2: 'Opensource, API based service, Plug and Play',
        title3: 'Future-Proof Architecture',
        description3: 'Fraud Proof and Validity Proof',
        title4: 'Customizable & Upgradable',
        description4: 'Open source and auditable',
      },
      advantages: {
        badge: 'Advantages',
        num1: 'Faster development timeline',
        num2: 'Low gas cost with aggregation',
        num3: 'Layer 1 Equivalent Security',
      },
      usecase: {
        badge: 'Usecase',
        case1: 'Bitcoin Layer2',
        case2: 'Wrapping BTC',
        case3: 'Bitcoin Bridge',
        case4: 'Bitcoin Staking Protocol',
        start: 'get started right now !',
        early: 'Early Access',
      },
    },
    raffle: {
      draw: 'draw',
      rate: 'up to {{rate}}% return',
      getFreeDraw:
        'get <span className="bl-text-primary group-hover/title:bl-text-white">free</span> draw',
      getFreeDrawTip: 'Invite a friend to make a draw, you could get one more free chance',
      freeDraw: 'free draw',
      oneDraw: '1 Draw',
      tenDraw: '10 Draw',
      hundredDraw: '100 Draw',
      '10%off': '10% off',
      guaranteedComponent:
        'Guaranteed Of One <span class="bl-text-primary bl-px-1">4-Star</span> Part',
      skip: 'Skip',
      superCard: {
        title: 'Super Racer Draw',
        more: 'More',
        myEstimatedTotalAirdrop: 'My Estimated Total Airdrop:',
      },
      learnMore: {
        label: 'Learn More',
        title: 'Guides To Get More Airdrop',
        tip: 'Please read this guide carefully to understand the game rules, as it contains the the secret of wealth!',
        description:
          "The official subsidy for this event will be <span class='bl-text-primary'>100%</span> (in BTR). All drawing fees from users will be put into the airdrop pool,  and the final BTR reward is <span class='bl-text-primary'>200%</span> of the amount in the pool. No worry of being rejected",

        section1: {
          title: 'why we play',
          description1: `Assembling cars in this game will receive <span class="bl-text-primary">unlocked $BTR token</span> airdrop when Bitlayer TGE. This airdrop pool includes all drawing fee spent and <span class="bl-text-primary">100%</span> subsidy by Bitlayer.`,
          description2: `Owning different levels of cars indicates a different airdrop return rate. The highest return rate is approximately <span class="bl-text-primary">200%</span>.`,
        },
        section2: {
          title: 'How we play',
          description1:
            "There are three levels （<img class='bl-inline bl-h-2 md:bl-h-[17px]' src='/images/user-center/raffle/star-3.png' alt='three stars'>、<img  class='bl-inline bl-h-2 md:bl-h-[17px]' src='/images/user-center/raffle/star-4.png' alt='four stars'> 、<img class='bl-inline bl-h-2 md:bl-h-[17px]' src='/images/user-center/raffle/star-5.png' alt='five stars'>）of cars in the game, assembled by 8 different parts. Collecting all 8 different parts of the same level can assemble a car.",
          description2:
            'Using 10 DRAW increases the probability of obtaining higher level parts and reduces costs',
          description3: '10 times of 10 DRAW, at least one 5-star part will be obtained',
          description4: 'The amount of high level cars you own determines your return rate',
        },
      },
      collection: {
        myEstimatedTotalAirdrop: `My <span class="bl-text-primary">Estimated </span>Total Airdrop`,
        valueTips: {
          tip1: 'The estimate total airdrop value is the estimated value of the $BTR you will receive (based on the TGE price of $BTR)',
          tip2: 'Since all assembled cars share the airdrop reward pool, the estimated airdrop amount will change at any time',
          tip3: 'The estimated airdrop amount does not represent the final value gain you will receive',
        },
        title: '<span class="bl-text-primary">airdrop</span> collection',
        ready: 'ready to assemble',
        collection: 'collection',
        stars: 'stars',
        estimated: 'Estimated',
        cars: {
          engine: 'engine',
          wheels: 'wheels',
          steeringwheel: 'steering wheel',
          discbrake: 'disc brake',
          chassisrigged: 'chassis rigged',
          truckhitch: 'truck hitch',
          seat: 'seat',
          gascylinder: 'gas cylinder',
        },
      },
      drawAd: {
        title: 'one more 10 draw to assemble your car',
        myStarCollection: 'My {{ star }}-Star collection',
        '3Star-title': `<span class='bl-text-primary'>1or2</span> 10 Draw`,
        '3Star-description1': `To assemble a new car`,
        '4Star-title': `<span class='bl-text-primary'>Really</span> close`,
        '4Star-description1': `To assemble high value 4-Star cars`,
        '5Star-title': `<span class='bl-text-primary'>Really</span> close`,
        '5Star-description1': `To assemble top value 5-Star cars`,
        '5Star-description2': `And get ROI up to <span class='bl-text-primary'>200%</span>`,
      },
      oopsSomethingWrong: 'oops, something wrong',
      insufficientUSDTBalance: 'Insufficient USDT Balance',
      insufficientBTCGas: 'Insufficient BTC Gas',
      needSomeUSDTOrGas: 'Need Some USDT or Gas?',
    },
    miningGalaNew: {
      open: 'Open',
      confirm: 'Confirm',
      oops: 'oops, something wrong',
      banner: {
        racer: 'Racer',
        won: 'Won',
        taskTo: 'Task To Get box',
        daily: 'Daily',
        treasureBox: 'Treasure Box',
        premiumBox: 'Premium Box',
      },
      normalBox: {
        getMoreBox: 'Complete Tasks to Get More Boxes',
        completeTask: 'Complete Task',
        inviteFriends: 'Invite Friends',
        noBox: `Today's paid boxes have all been opened, please come back tomorrow`,
        invite: 'Invite',
        toGetBox: 'To Get Box',
        taskToOpen: 'Task To Open',
        valueOpen: '~$0.5 Open',
      },
      premiumBox: {
        openPremiumBoxGet: 'Open premium box get',
        accumulated: 'Accumulated Unboxing of',
        boxGetOne: 'Boxes Get 1 Premium Box',
      },
      airDropDialog: {
        congratulations: 'Congratulations !',
        hold: 'Hold',
        luckyHelmet: 'Lucky Helmet',
        pioneerBadgeHolder: 'Pioneer Badge Holder',
        willGet: 'Will Get',
        treasureBoxesAirdrop: 'Treasure Boxes Airdrop',
        youHavewon: 'You have won',
        confirm: 'Confirm',
        pleaseOpenBefore: 'Please open before {{date}} to avoid expiration!',
      },
      btcDialog: {
        insufficient: 'Insufficient BTC balance',
        insufficientDesc: 'Your BTC balance is insufficient for this transaction.',
        flashBridge: 'Flash Bridge-In',
      },
      inviteDialog: {
        invite: 'Invite and get free',
        boxes: 'boxes',
        twitter: 'Twitter',
        telegram: 'Telegram',
        inviteDesc:
          'Share your referral code/link to your friends, as they open at least 2 treasure boxes, you will get one free box',
      },
      limitTag: {
        amount: 'Amount',
        countdown: 'Countdown',
        end: 'Airdropped boxes will expire after the countdown ends!',
      },
      outDialog: {
        outDesc: 'Sorry, the boxes are out now! Stay tuned for more airdrop events soon!',
      },
      unboxDialog: {
        failed: 'Failed To Confirm On-Chain',
        tryAgain: 'Try Again',
        total: 'Total',
        boxes: 'Boxes',
        gotIt: 'Got It',
        confirmTransaction: 'Confirm Transaction In Wallet...',
        spend: ' You will spend about',
      },
      pool: {
        finished: 'Finished',
        tutorial: 'Tutorial',
        meson: 'Meson Bridge',
        mini: 'Mini Bridge',
        go: 'GO',
        rewards: 'Rewards',
        boxes: 'Boxes',
        boxesTip: 'Each time completing the daily task',
        badge: 'Badge',
        whitelist: 'Whitelist',
        task: 'Task',
        verify: 'verify',
        daily: 'daily',
        deposit: 'Deposit',
        followX: 'Follow X',
        website: 'Website',
        twitter: 'Twitter',
        audit: 'Audit Report',
        'Deposit 0.0001 BTC': 'Deposit 0.0001 BTC',
        'Trade BTC option': 'Trade BTC option',
        'Supply ≥ 0.000076 WBTC': 'Supply ≥ 0.000076 WBTC',
        'Supply ≥ 0.0001 WBTC': 'Supply ≥ 0.0001 WBTC',
        'Deposit ≥ 0.0001 stBTC to Avalon': 'Deposit ≥ 0.0001 stBTC to Avalon',
      },
    },
    btcfi: {
      sbt: {
        canMint: 'You can mint a Bitlayer x Binance wallet SBT',
        canNotmint: 'It seems that you could not mint Bitlayer x Binance wallet SBT',
        hasMinted: 'You have minted a Bitlayer x Binance wallet SBT',
        waiting: 'Waiting For On-Chain Confirm ~10s',
        mint: 'Mint',
        cancel: 'Cancel',
        minted: 'Minted',
      },
      yield: {
        btcfi: {
          service: '- The financial service is provided by Bitfi Protocol -',
          redeem: 'Claim&Redeem',
          stake: 'Stake',
          claim: 'Claim points',
          reward: 'Points reward:',
          points: 'points',
          accept:
            'I accept the <a href="https://docs.bitlayer.org/docs/Learn/Terms/BTCFi/BitFi/user-agreement/" target="_blank" >BTCYield user agreement</a> & <a href="https://docs.bitlayer.org/docs/Learn/Terms/BTCFi/BitFi/disclaimer" target="_blank">Bitlayer Disclaimer</a>',
          finalYield: "Bitfi's final yield for users:",
          yieldDesc1:
            '{{percent}} is the historical yield reference based on quantitative data. Settled once every 30 days, BTC can be claimed after settlement, Yield may vary depending on network conditions.',
          yieldDesc2:
            'Bitlayer provides Bitlayer points subsidy. Settled every day. Points can be claimed while redeeming the principal.',
          stakeDesc: 'Stake BTC into Bitfi BFBTC Pool | Get rewards in BTC+Points',
          stakeDesc1: 'Please deposit your principal from www.bitlayer.org/btcfi only.',
          stakeDesc2:
            'Depositing from other pages or on-chain contracts may result in no returns and a loss of the principal.',
          stakeDesc3:
            'After staking, you will receive BFBTC tokens. To claim rewards and redeem the principal, you need to send back BFBTC.',
          stakeDesc4: 'You need to hold BFBTC in your wallet to earn Bitlayer Points.',
          redeemTitle: 'Claim & Redeem',
          redeemDesc1: `Your can claim BTC in <span class='bl-text-[#1B1E21]'>7days</span> after apply to redeem.`,
          redeemDesc2: `BTR reward could be claimed <span class='bl-text-[#1B1E21]'>after TGE</span>.`,
        },
        canivaLink: 'Bitlayer BTCFi Canivald',
        balance: 'My Balance',
        totalValue: 'Total value',
        apr: 'APR',
        apy: 'APY',
        rewards: 'My rewards',
        claim: 'Claim',
        max: 'Max',
        service: '- The financial service is provided by Desyn Protocol -',
        redeem: 'Redeem',
        stake: 'Stake',
        details: 'More Detaills',
        yieldsFrom: 'Yields from',
        supportTokens: 'Support tokens',
        rewardTokens: 'Reward Tokens',
        yieldsType: 'Yields Type',
        yieldsSource: 'Yields Source:',
        claimed: 'Claimed',
        claime: 'Claime',
        ended: 'Ended',

        accept:
          'I accept the <a href="https://docs.bitlayer.org/docs/Learn/Terms/BTCFi/Desyn/user-agreement" target="_blank" >BTCYield user agreement</a> & <a href="https://docs.bitlayer.org/docs/Learn/Terms/BTCFi/Desyn/disclaimer" target="_blank">Bitlayer Disclaimer</a>',
        finalYield: `Desyn's final yield for users:`,
        yieldDesc1:
          '{{percent}} is the historical yield reference based on quantitative data. Settled once every 30 days, WBTC can be claimed after settlement, Yield may vary depending on network conditions.',
        yieldDesc2:
          'Bitlayer provides a BTR token subsidy of {{percent}}. Settled every day. BTR can be claimed after TGE.',
        yieldDesc3:
          'Desyn provides a DSN token subsidy of {{percent}}. Settled every day. DSN can be claimed after TGE.',
        claimTitle: 'Claim Reward',
        claimTip: 'You can claim <span>{{total}}WBTC</span> now.',
        estimatedTip: '{{next}} WBTC(estimated) will be settled at {{time}}.',
        cliamOtherTip: 'BTR and DSN token could be claimed after TGE.',
        stakeTitle: 'BTCYield Stake',
        stakeDesc: 'Stake BTC into Desyn BLBTC Pool | get rewards in BTC+DSN+BTR',
        stakeDesc1: 'Please deposit your principal from www.bitlayer.org/btcfi only.',
        stakeDesc2:
          'Depositing from other pages or on-chain contracts may result in no retums and a loss of the principal.',
        stakeDesc3:
          'After staking, you will receive BLBTC token. To redeem the principal, you need to send back BLBTC.',
        stakeDesc4:
          'The earnings will be associated with the duration you hold the BLBTC in your wallet.',
        redeemTitle: 'BTCYield Redeem',
        applyToRedeem: 'Apply to redeem',
        redeemDesc:
          'Due to calculation precision issues, the displayed value of your principal may experience a very slight change (the value may increase or decrease, not exceeding 0.00000001 BTC). This is normal; please be aware.',
        redeemDesc1: `Your tokens will be sent to your wallet in <span class='bl-text-[#1B1E21]'>7days</span> after redeem.`,
        redeemDesc2: `Rewards from redeemed tokens in this settlement period will be acaliable <span class="bl-text-[#171717]"> after the settlement date.</span>`,
        myRedeem: 'My Redeem',
        redeemNow: 'Redeem now',
        waitApproval: 'Wait for approval',
        waitDays: 'in {{count }} days',
        stakeSuccess: 'Stake Success',
        recheck: 'RECHECK',
        checkin: 'CHECKIN',
        dailyCheckIn: 'Daily Check-in',
        yieldBonus: 'Yield Bonus',
      },
    },
  },
};
