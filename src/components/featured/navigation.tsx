import { cn } from '@/lib/utils';
import { useNavigate, useLocation } from '@remix-run/react';
import { Link } from '@/components/i18n/link';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import { HeadMenu, LinkData, LinkRendererProps } from './header';
// import SpannerIcon from '@/components/icons/SpannerIcon';
// import Exchange2Icon from '@/components/icons/Exchange2Icon';
// import FaucetIcon from '@/components/icons/Faucet2Icon';
import RaceFinishIcon from '@/components/icons/RaceFinishIcon';
import BridgeIcon from '@/components/icons/BridgeIcon';
import FlashIcon from '@/components/icons/FlashIcon';
import RacerCenterIcon from '@/components/icons/RacerCenterIcon';
import SuperIcon from '@/components/icons/SuperIcon';
import BadgeIcon from '@/components/icons/BadgeIcon';
import BtcfiIcon from '@/components/icons/BtcfiIcon';
import GalaIcon from '@/components/icons/GalaIcon';
// import BagIcon from '@/components/icons/BagIcon';
// import ArticleIcon from '@/components/icons/ArticleIcon';
import EtherscanIcon from '@/components/icons/EtherscanIcon';
import { bitlayerMainnet, bitlayerTestnet } from '@/wallets/config/chains';
import { useMediaQuery } from '@react-hook/media-query';
import { Drawer, DrawerContent, DrawerPortal, DrawerTrigger } from '@/components/ui/drawer';
import MenuIcon from '@/components/icons/MenuIcon';
import { useCallback, useContext, useState } from 'react';
import { HeaderContext, headerWalletChain } from '@/hooks/header';
import { AnimatePresence } from 'framer-motion';
import { useScroll } from '@use-gesture/react';
import { HeadWalletButton } from './wallet';
import { useAtomValue } from 'jotai';
import WorldIcon from '@/components/icons/WorldIcon';
import AirdropIcon from '@/components/icons/AirdropIcon';
import HelmetIcon from '@/components/icons/HelmetIcon';
import FireVoteIcon from '@/components/icons/FireVoteIcon';
import { useTranslation } from 'react-i18next';
import MiningGalaNavIcon2 from '@/components/icons/mining-gala/NavIcon2';
import { USDCOutlineIcon } from '../icons/coins/USDCIcon';
import MatrixCubeIcon from '../icons/MatrixCubeIcon';

const isDev = import.meta.env.MODE === 'development';

export const defaultChain = isDev ? bitlayerTestnet : bitlayerMainnet;

// const resourceLinks = [
//   {
//     name: 'navigation.links.brandKit',
//     link: 'https://github.com/bitlayer-org/bitlayer-materials',
//     icon: BagIcon,
//   },
//   {
//     name: 'navigation.links.whitepaper',
//     link: 'https://static.bitlayer.org/Bitlayer-Technical-Whitepaper.pdf',
//     icon: ArticleIcon,
//   },
// ];

// const TestnetDocsLinks = {
//   Documentation: 'https://docs.bitlayer.org/docs/Build/GettingStarted/QuickStart',
//   'Build Now': 'https://docs.bitlayer.org/docs/Build/GettingStarted/QuickStart/',
//   'The Graph': 'https://docs.bitlayer.org/docs/Build/DeveloperResources/Indexers/TheGraph',
//   'Get start': 'https://bl-docs-test.pages.dev/docs/Build/GettingStarted/QuickStart',
//   'Run a node': 'https://bl-docs-test.pages.dev/docs/Build/GettingStarted/CompileAndRun',
//   'Bitlayer Architecture': 'https://bl-docs-test.pages.dev/docs/Learn/Introduction/TechnicalArchitecture',
//   'Bitlayer Roadmap': 'https://docs.bitlayer.org/docs/Learn/Introduction/Roadmap/',
//   'Dapp Security Manual': 'https://bl-docs-test.pages.dev/docs/Learn/TrackPack/DappSecurityMannual',
//   'Security Network': 'https://bl-docs-test.pages.dev/docs/Learn/TrackPack/SecurityNetwork',
//   'Opensource tools': 'https://bl-docs-test.pages.dev/docs/Learn/TrackPack/OpensourceTools',
//   'Explore More Tools': 'https://bl-docs-test.pages.dev/docs/Learn/TrackPack/BoostTools',
//   'More Supports': 'https://bl-docs-test.pages.dev/docs/Learn/TrackPack/OperationSupport'
// }
const MainNetDocsLinks = {
  Documentation: 'https://docs.bitlayer.org/docs/Build/GettingStarted/QuickStart',
  'Build Now': 'https://docs.bitlayer.org/docs/Build/GettingStarted/QuickStart/',
  'The Graph': 'https://docs.bitlayer.org/docs/Build/DeveloperResources/Indexers/TheGraph',
  'Get start': 'https://docs.bitlayer.org/docs/Build/GettingStarted/QuickStart',
  'Run a node': 'https://docs.bitlayer.org/docs/Build/GettingStarted/CompileAndRun',
  'Bitlayer Architecture': 'https://docs.bitlayer.org/docs/Learn/Bitlayer%20Rollup/system-design/',
  'Bitlayer Roadmap': 'https://docs.bitlayer.org/docs/Learn/Introduction/Roadmap/',
  'Dapp Security Manual': 'https://docs.bitlayer.org/docs/Build/TrackPack/DappSecurityMannual',
  'Security Network': 'https://docs.bitlayer.org/docs/Build/TrackPack/SecurityNetwork',
  'Opensource tools': 'https://docs.bitlayer.org/docs/Build/TrackPack/OpensourceTools/',
  'Explore More Tools': 'https://docs.bitlayer.org/docs/Build/TrackPack/BoostTools/',
  'More Supports': 'https://docs.bitlayer.org/docs/Learn/TrackPack/OperationSupport',
};

export const DocsLinks = MainNetDocsLinks;

const userLinks = [
  {
    name: 'navigation.links.dappCenter',
    link: '/ready-player-one/dapps-center',
    icon: MiningGalaNavIcon2,
    hint: 'navigation.links.dappCenterEcosystem',
  },
  {
    name: 'navigation.links.btcfi',
    link: '/btcfi',
    icon: BtcfiIcon,
    hint: 'navigation.links.btcfiDesc',
  },
  {
    name: 'navigation.links.miningGala3',
    link: '/mining-gala',
    icon: GalaIcon,
    hint: 'navigation.links.developersSection.developerSupport.miningGalaHint',
  },
  {
    name: 'navigation.links.readyPlayerOne',
    link: '/airdrop/ready-player-one',
    icon: RaceFinishIcon,
    hint: 'navigation.links.hint1',
  },
  {
    name: 'navigation.links.luckyHelmet',
    link: '/airdrop/lucky-helmet',
    icon: HelmetIcon,
    hint: 'navigation.links.hint2',
  },
  {
    name: 'navigation.links.leaderboard',
    link: '/ready-player-one/leaderboard',
    icon: FireVoteIcon,
    hint: 'navigation.links.dappCenterHint',
  },
];

const bridgeLinks = [
  {
    name: 'navigation.links.bridge',
    link: '/bridge',
    icon: BridgeIcon,
  },
  {
    name: 'navigation.links.flash',
    link: '/flash-bridge',
    icon: FlashIcon,
  },
  {
    name: 'navigation.links.usdcChange',
    link: '/usdc-change',
    icon: USDCOutlineIcon,
  },
  {
    name: 'navigation.links.quickJump',
    link: '/quick-jump',
    icon: MatrixCubeIcon,
  },
];

const racerCenterLinks = {
  name: 'navigation.links.userCenter',
  default: 'navigation.links.userCenter',
  link: '/me',
  children: [
    {
      name: 'navigation.links.userCenter',
      link: '/me',
      icon: RacerCenterIcon,
    },
    {
      name: 'navigation.links.superDraw',
      link: '/assemble-cars',
      icon: SuperIcon,
    },
    {
      name: 'navigation.links.badge',
      link: '/me/badge',
      icon: BadgeIcon,
    },
  ],
};

const languageLinks = [
  {
    name: 'English',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="en" link="" />,
  },
  {
    name: '繁體中文',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="zh-HC" link="/zh-HC" />,
  },
  {
    name: 'Tiếng Việt',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="vi" link="/vi" />,
  },
  {
    name: '简体中文',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="zh-CN" link="/zh-CN" />,
  },
  {
    name: '日本語',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="ja" link="/ja" />,
  },
  // {
  //   name: '한국어',
  //   render: (props: LinkRendererProps) => <I18nLink {...props} lang="ko" link="/ko" />,
  // },
  {
    name: 'bahasa Indonesia',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="id" link="/id" />,
  },
  {
    name: 'Русский',
    render: (props: LinkRendererProps) => <I18nLink {...props} lang="ru" link="/ru" />,
  },
];

const navigationLinks: LinkData[] = [
  {
    name: 'navigation.links.btcfi',
    link: '/btcfi',
  },
  {
    name: 'navigation.links.bridge',
    default: 'navigation.links.bridge',
    children: bridgeLinks,
  },
  {
    name: 'navigation.links.ecosystem',
    children: userLinks,
    icon: AirdropIcon,
  },
];

const LanLink = {
  name: '',
  icon: WorldIcon,
  children: languageLinks,
};

const I18nLink = ({
  name,
  link,
  lang,
  className,
}: {
  name: string;
  link: string;
  lang: string;
  className?: string;
}) => {
  const navigate = useNavigate();
  const { pathname, search } = useLocation();
  const { i18n } = useTranslation();

  const handleClick = () => {
    const baseUrl = pathname.replace(/\/(zh-CN|vi|zh-HC|ja|ko|id|ru)/g, '');
    i18n.changeLanguage(lang);

    navigate({
      pathname: `${link}${baseUrl}`,
      search,
    });
  };
  return (
    <button className={className} onClick={handleClick}>
      {name}
    </button>
  );
};

const NavigationItemContent = ({ link, className }: { link: LinkData; className?: string }) => {
  const { t } = useTranslation();
  const [value, setValue] = useState(link.default || '');

  if (!link.children) {
    return (
      <NavigationMenuLink className={navigationMenuTriggerStyle()} asChild>
        {link.render ? (
          link.render(link)
        ) : link.link ? (
          <Link
            to={link.link}
            className="bl-gap-2 hover:bl-bg-transparent data-[active]:bl-bg-transparent data-[state=open]:bl-bg-transparent"
          >
            {link.icon && <link.icon className="bl-size-5" />}
            {t(link.name)}
          </Link>
        ) : (
          <div className="bl-gap-2 bl-cursor-pointer hover:bl-bg-transparent data-[active]:bl-bg-transparent data-[state=open]:bl-bg-transparent">
            {link.icon && <link.icon className="bl-size-5" />}
            {t(link.name)}
          </div>
        )}
      </NavigationMenuLink>
    );
  }

  const handleClickTrigger = (e: React.MouseEvent) => {
    e.preventDefault();
  };

  return (
    <>
      <NavigationMenuTrigger
        className="bl-gap-2 hover:bl-bg-transparent data-[active]:bl-bg-transparent data-[state=open]:bl-bg-transparent"
        onClick={handleClickTrigger}
      >
        {link.link ? (
          <Link
            to={link.link}
            className="bl-gap-2 hover:bl-bg-transparent data-[active]:bl-bg-transparent data-[state=open]:bl-bg-transparent"
          >
            {link.icon && <link.icon className="bl-size-5" />}
            {t(link.name)}
          </Link>
        ) : (
          <>
            {link.icon && <link.icon className="bl-size-5" />}
            {t(link.name)}
          </>
        )}
      </NavigationMenuTrigger>
      <NavigationMenuContent className={className}>
        <HeadMenu
          links={link.children || []}
          showOrder={link.showOrder}
          linkComponent={NavigationMenuLink}
          value={value}
          defaultValue={link.default}
          onValueChange={setValue}
          // onMouseHoverItem={onHover}
        />
      </NavigationMenuContent>
    </>
  );
};

const DesktopNavigation = () => {
  return (
    <div className="bl-flex">
      <NavigationMenu>
        <NavigationMenuList>
          {navigationLinks.map((link, index) => (
            <NavigationMenuItem key={index} value={index.toString()}>
              <NavigationItemContent
                className={cn('', { 'bl-min-h-[500px]': index === 0 })}
                link={link}
              />
            </NavigationMenuItem>
          ))}
        </NavigationMenuList>
      </NavigationMenu>
      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem value="user-center">
            <NavigationItemContent link={racerCenterLinks} className="bl-min-w-[360px]" />
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>
      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem value="lan">
            <NavigationItemContent link={LanLink} className="bl-min-w-[260px]" />
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>
    </div>
  );
};

const MobileNavigation = () => {
  const [open, setOpen] = useState(false);
  const mobileLanLink = {
    ...LanLink,
    name: 'navigation.links.language',
  };
  return (
    <Drawer direction="right" shouldScaleBackground={false} open={open} onOpenChange={setOpen}>
      <DrawerTrigger className="btn-mask bl-p-2 bl-text-primary bl-duration-150 hover:bl-bg-accent/50 hover:bl-text-accent-foreground bl-order-last md:bl-order-none">
        <MenuIcon className="bl-size-6" />
      </DrawerTrigger>
      <DrawerPortal>
        <DrawerContent className="bl-fixed bl-top-0 bl-bottom-0 bl-right-0 bl-h-dvh bl-w-[92vw] bl-bg-primary focus-visible:bl-outline-none">
          <div className="bl-h-full bl-flex bl-gap-4 bl-p-4 bl-flex-1 bl-overflow-y-scroll">
            <HeadMenu
              links={[...navigationLinks, racerCenterLinks, mobileLanLink]}
              showOrder={false}
              showSubOrder={true}
              onClick={() => setOpen(false)}
            />
          </div>
        </DrawerContent>
      </DrawerPortal>
    </Drawer>
  );
};

export default function NavigationHeader() {
  const [hidden, setHidden] = useState(false);
  const { widget, pinned } = useContext(HeaderContext);
  const isDesktop = useMediaQuery('(min-width: 768px)');

  const setHiddenByPin = useCallback(
    (state: boolean) => {
      if (pinned) {
        state = false;
      }
      setHidden(state);
    },
    [pinned],
  );

  useScroll(
    (state) => {
      const [, offsetY] = state.offset;
      if (offsetY === 0) {
        setHiddenByPin(false);
        return;
      }
      if (offsetY < 0) {
        return;
      }
      const [, directionY] = state.direction;
      if (directionY === 1) {
        setHiddenByPin(true);
      } else {
        setHiddenByPin(false);
      }
    },
    { target: window },
  );

  const chain = useAtomValue(headerWalletChain);

  return (
    <header
      className={cn(
        'bl-h-[70px] md:bl-h-20 bl-w-screen bl-fixed bl-z-30 bl-top-0 bl-left-0 bl-bg-background/80 bl-border-b bl-border-divider bl-duration-300 bl-ease-linear bl-font-body',
        {
          'bl-translate-y-[-100%]': hidden,
        },
      )}
    >
      <div className="bl-container bl-flex bl-w-full bl-h-full bl-items-center bl-justify-between bl-gap-4 md:bl-gap-10 bl-px-4 md:bl-px-0">
        <div className="bl-flex bl-gap-3 md:bl-gap-8">
          <Link className="bl-block bl-min-w-0 bl-shrink-0" to="/">
            <img
              src="/images/bitlayer-logo.png"
              className="bl-hidden md:bl-block bl-h-[36px]"
              alt="bitlayer"
            />
            <img src="/images/bitlayer.gif" className="bl-size-8 md:bl-hidden" alt="bitlayer" />
          </Link>
          <AnimatePresence>{widget}</AnimatePresence>
        </div>

        <div className="bl-grow md:bl-hidden"></div>

        <div className="bl-flex bl-gap-2 md:bl-gap-4 bl-items-center bl-justify-end md:bl-grow md:bl-w-full bl-order-last md:bl-order-none">
          {isDesktop ? <DesktopNavigation /> : <MobileNavigation />}
        </div>

        <div className="bl-w-36 md:bl-w-44">
          <HeadWalletButton chain={chain || defaultChain} />
        </div>
      </div>
    </header>
  );
}
