import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { BtcfiV2APYHistoryItem } from '@/modules/btcfi/types';
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from 'recharts';

const chartConfig = {
  apy: {
    label: 'APY',
    color: 'var(--chart-1)',
  },
} satisfies ChartConfig;

export function APYChart({ data }: { data: BtcfiV2APYHistoryItem[] }) {
  return (
    <ChartContainer config={chartConfig} className="bl-h-[320px] bl-aspect-auto">
      <AreaChart
        accessibilityLayer
        data={data}
        margin={{
          left: 12,
          right: 12,
          top: 24,
        }}
      >
        <CartesianGrid vertical={false} horizontal={false} />
        <XAxis
          dataKey="timestamp"
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          tickFormatter={(value) => {
            return new Date(value * 1000).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
            });
          }}
        />
        <YAxis
          dataKey="apy"
          axisLine={false}
          tickLine={false}
          tickFormatter={(value) => `${value * 100}%`}
        />
        <ChartTooltip
          cursor={{
            stroke: '#E36E1B',
            strokeWidth: 1,
            strokeDasharray: '5 5',
          }}
          content={<ChartTooltipContent indicator="line" />}
        />
        <defs>
          <linearGradient id="paint0_linear_28981_16090" x1="0" y1="0" x2="0" y2="1">
            <stop stopColor="#813A14" />
            <stop offset="25%" stopColor="#50240C" stopOpacity="0.615812" />
            <stop offset="85%" stopColor="#351808" stopOpacity="0.407874" />
            <stop offset="1" stopOpacity="0" />
          </linearGradient>
        </defs>
        <Area
          dataKey="apy"
          type="natural"
          fill="url(#paint0_linear_28981_16090)"
          fillOpacity={1}
          stroke="#E36E1B"
          strokeWidth={2}
        />
      </AreaChart>
    </ChartContainer>
  );
}
