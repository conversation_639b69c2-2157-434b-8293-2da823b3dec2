import { WalletConnector } from '@/components/featured/wallet';
import { Button } from '@/components/ui/button';
import { AmountInput } from '@/components/ui/form-field';
import { DynamicIcon } from '@/components/ui/icon';
import { useAccount } from '@/hooks/wallet/account';
import { useWalletBalance } from '@/hooks/wallet/balance';
import { BaseChainType, Token } from '@/wallets/config/type';
import { ArrowDownIcon } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { parseUnits } from 'viem';

export type DepositFormProps = {
  chain: BaseChainType;
  sourceToken: Token;
  targetToken: Token;
};

export function DepositForm({ sourceToken, targetToken, chain }: DepositFormProps) {
  const { t } = useTranslation();
  const { address, chain: currentChain } = useAccount({ network: chain.networkType });
  const [amount, setAmount] = useState('');

  const { data: balance } = useWalletBalance({
    network: chain.networkType,
    address,
    token: sourceToken,
    chain,
  });

  const { data: targetBalance } = useWalletBalance({
    network: chain.networkType,
    address,
    token: targetToken,
    chain,
  });

  const amountValidator = (value: string) => {
    try {
      parseUnits(value, sourceToken.decimals);
    } catch (error) {
      return false;
    }

    const [, decimal] = value.split('.');
    if (decimal && decimal.length > sourceToken.decimals) {
      return false;
    }

    return !isNaN(Number(value)) && Number(value) >= 0;
  };

  const handleClickMax = () => {
    if (balance) {
      setAmount(balance.formatted);
    }
  };

  const renderActionButton = () => {
    if (!address || !currentChain) {
      return (
        <WalletConnector chain={chain}>
          <Button className="bl-w-full" overlayFrom="none">
            {t('common.connect')}
          </Button>
        </WalletConnector>
      );
    }

    if (currentChain.id !== chain.id) {
      return (
        <Button
          className="bl-w-full"
          overlayFrom="none"
          onClick={handleSwitch}
          disabled={isSwitching}
        >
          {isSwitching && <LoaderIcon className="bl-size-6 bl-animate-spin bl-mr-2" />}
          {t('common.switchChain', { chainName: chain.name })}
        </Button>
      );
    }
  }

  return (
    <div className="bl-space-y-3">
      <div className="bl-border bl-border-card-border bl-p-5 bl-space-y-3.5 bl-bg-black bl-relative">
        <div>Deposit</div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <AmountInput
            value={amount}
            onChange={setAmount}
            validator={amountValidator}
            className="bl-w-30"
            placeholder="0.00"
          />
          <Button variant="outline-3" className="bl-w-32">
            <div className="bl-flex bl-items-center bl-gap-2">
              <DynamicIcon icon={sourceToken.icon} className="bl-size-5" />
              {sourceToken.symbol}
            </div>
          </Button>
        </div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <div>$ 0</div>
          <div className="bl-flex bl-gap-2">
            <span>
              {balance ? balance.formatted : '--'} {sourceToken.symbol}
            </span>
            <button className="bl-text-primary" onClick={handleClickMax}>
              MAX
            </button>
          </div>
        </div>
        <div className="bl-absolute -bl-bottom-8 bl-left-1/2 -bl-translate-x-1/2">
          <div className="bl-size-[42px] lg:bl-size-12 bl-rounded-full bl-bg-primary bl-text-black bl-flex bl-items-center bl-justify-center">
            <ArrowDownIcon className="bl-size-6" />
          </div>
        </div>
      </div>
      <div className="bl-border bl-border-card-border bl-p-5 bl-space-y-3.5 bl-bg-black">
        <div>Get</div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <div className="bl-text-4xl bl-text-indicator">0.00</div>
          <Button variant="outline-3" className="bl-w-32 disabled:bl-opacity-100" disabled>
            <div className="bl-flex bl-items-center bl-gap-2">
              <DynamicIcon icon={targetToken.icon} className="bl-size-5" />
              {targetToken.symbol}
            </div>
          </Button>
        </div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <div>$ 0</div>
          <div className="bl-flex bl-gap-2">
            <span>
              {targetBalance ? targetBalance.formatted : '--'} {targetToken.symbol}
            </span>
          </div>
        </div>
      </div>
      <Button overlayFrom="none" className="bl-w-full">
        <span>Confirm</span>
      </Button>
    </div>
  );
}
