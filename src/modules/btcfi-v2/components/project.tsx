import React from 'react';
import { SUIShape } from '@/components/icons/coins/SUIIcon';
import { cn } from '@/lib/utils';
import { BtcfiV2Project } from '@/modules/btcfi/types';
import { AVAXShape } from '@/components/icons/coins/AVAXIcon';
import BitlayerLogo from '@/components/icons/BitlayerLogo';
import { DynamicIcon } from '@/components/ui/icon';
import { Link } from '@/components/i18n/link';

type ProjectCard = {
  className?: string;
  project: BtcfiV2Project;
};

const backgroundShapes: Record<string, () => React.ReactNode> = {
  sui: () => {
    return (
      <SUIShape className="bl-w-[202px] bl-h-[258px] bl-absolute bl-bottom-[-30px] bl-right-[-53px] bl-opacity-5" />
    );
  },
  avalanche: () => {
    return (
      <AVAXShape className="bl-size-[194px] bl-absolute bl-bottom-[-13px] bl-right-[-51px] bl-opacity-5" />
    );
  },
  bitlayer: () => {
    return (
      <BitlayerLogo className="bl-w-[213px] bl-h-[194px] bl-absolute bl-bottom-[-13px] bl-right-[-74px] bl-opacity-5" />
    );
  },
};

export function ProjectCard({ className, project }: ProjectCard) {
  const badge =
    project.tag === 'btr' ? (
      <ProjectCardBadge variant="primary">BTR Allowance</ProjectCardBadge>
    ) : project.tag === 'coming' ? (
      <ProjectCardBadge variant="secondary">Coming soon</ProjectCardBadge>
    ) : null;

  const chain = project.chain?.toLowerCase().split('_')[0] || '';
  const backgroundShape = backgroundShapes[chain]?.();

  return (
    <ProjectCardBase
      id={project.id}
      name={project.name}
      icon={project.icon}
      apr={project.apy.toString()}
      badge={badge}
      backgroundShape={backgroundShape}
      disabled={project.tag === 'coming'}
      className={className}
    />
  );
}

type ProjectCardProps = {
  id: number;
  name: string;
  icon: React.FC<React.SVGProps<SVGSVGElement>> | string;
  apr: string;
  badge?: React.ReactNode;
  backgroundShape?: React.ReactNode;
  disabled?: boolean;
  className?: string;
};

export function ProjectCardBase({
  id,
  name,
  icon,
  apr,
  badge,
  backgroundShape,
  disabled,
  className,
}: ProjectCardProps) {
  const Comp = disabled ? 'div' : Link;
  return (
    <Comp
      to={`/btcfi-v2/projects/${id}`}
      className={cn(
        'bl-relative bl-w-[289px] bl-h-[134px] bl-p-5 bl-border bl-border-card-border bl-bg-gradient-to-b bl-from-[#242424] bl-to-black bl-overflow-hidden bl-duration-200 lg:bl-w-full lg:bl-h-[168px] lg:bl-p-8 hover:bl-border-primary',
        {
          'hover:bl-border-card-border': disabled,
        },
        className,
      )}
    >
      <div className="bl-flex bl-flex-col bl-justify-between bl-h-full">
        <div className="bl-flex bl-items-center bl-gap-4">
          <DynamicIcon icon={icon} className="bl-size-8 lg:bl-size-10" />
          <span className="bl-text-white bl-text-xl lg:bl-text-2xl">{name}</span>
        </div>
        <div className="bl-text-white bl-flex bl-gap-3.5 bl-text-xl lg:bl-text-2xl">
          <div>APY</div>
          <div className="bl-font-title bl-text-primary">{apr}</div>
        </div>
      </div>
      {badge}
      {backgroundShape}
    </Comp>
  );
}

type ProjectCardBadgeProps = {
  variant?: 'primary' | 'secondary';
  children?: React.ReactNode;
};

export function ProjectCardBadge({ children, variant }: ProjectCardBadgeProps) {
  return (
    <div
      className={cn(
        'bl-h-[22px] bl-px-2 bl-bg-primary/30 bl-text-primary bl-absolute bl-top-3 bl-right-3',
        {
          'bl-bg-secondary/30 bl-text-secondary': variant === 'secondary',
        },
      )}
    >
      {children}
    </div>
  );
}
