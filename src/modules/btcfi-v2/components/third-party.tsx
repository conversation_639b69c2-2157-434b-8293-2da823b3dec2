import AVAXIcon from '@/components/icons/coins/AVAXIcon';
import SUIIcon from '@/components/icons/coins/SUIIcon';
import { Button } from '@/components/ui/button';
import { BtcfiV2ThirdPartyCustody } from '@/modules/btcfi/types';
import { ArrowRightIcon, ArrowUpRightIcon } from 'lucide-react';
import React from 'react';

type ThirdPartyContentProps = {
  custody: BtcfiV2ThirdPartyCustody;
};

export function ThirdPartyContent({ custody }: ThirdPartyContentProps) {
  return (
    <div>
      <div>
        <div className="bl-flex bl-gap-5 bl-items-center">
          <NumberPoint>1</NumberPoint>
          <div className="bl-text-white bl-text-xl">Bridge</div>
        </div>
        <div className="bl-border-l bl-border-dashed bl-border-primary bl-my-2 bl-ml-3.5 bl-pl-4 bl-py-4 bl-flex bl-flex-col bl-items-center bl-gap-5">
          <div className="bl-grid bl-grid-cols-2 bl-gap-2 bl-w-full bl-relative">
            <Button variant="outline-3" className="bl-w-full disabled:bl-opacity-100" disabled>
              <div className="bl-flex bl-items-center bl-gap-2">
                <AVAXIcon className="bl-size-6" />
                YBTC.B
              </div>
            </Button>
            <Button variant="outline-3" className="bl-w-full disabled:bl-opacity-100" disabled>
              <div className="bl-flex bl-items-center bl-gap-2">
                <SUIIcon className="bl-size-6" />
                YBTC.B
              </div>
            </Button>
            <div className="bl-size-6 bl-rounded-full bl-border bl-border-input bl-absolute bl-top-1/2 -bl-translate-y-1/2 bl-left-1/2 -bl-translate-x-1/2 bl-bg-black bl-flex bl-items-center bl-justify-center">
              <ArrowRightIcon className="bl-size-4" />
            </div>
          </div>
          <Button className="bl-w-52" overlayFrom="none">
            <span>Go Bridge</span>
          </Button>
        </div>
      </div>

      <div>
        <div className="bl-flex bl-gap-5">
          <NumberPoint>2</NumberPoint>
          <div className="bl-text-white bl-text-xl">Earn from Bitfi</div>
        </div>
        <div className="bl-pl-2 bl-pt-4 bl-flex bl-flex-col bl-items-center">
          <div className="bl-grid bl-grid-cols-3 bl-gap-3.5">
            <Button variant="outline-3" className="bl-w-full">
              <span>Stake</span>
            </Button>
            <Button variant="outline-3" className="bl-w-full">
              <span>Unstake</span>
            </Button>
            <Button variant="outline-3" className="bl-w-full">
              <span>Claim</span>
            </Button>
          </div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width={241}
            height={40}
            viewBox="0 0 241 40"
            fill="none"
          >
            <path
              d="M1 0v19.088h119.5M240 0v19.088H120.5m0 0V0m0 19.088V40"
              stroke="#AEB5C5"
              strokeWidth={0.848}
            />
          </svg>
          <a href="/" className="bl-text-[22px] bl-text-white bl-flex bl-items-center bl-gap-1">
            Bitfi
            <ArrowUpRightIcon className="bl-size-4" />
          </a>
        </div>
      </div>

      <article className="btcfi-v2-risk">
        <h3>Risk</h3>
        <h4>Contract Risk:</h4>
        <p>
          If there are vulnerabilities in the Vault contract or cooperating protocol contracts, it
          may result in asset loss.
        </p>
        <h4>Strategy Execution Risk:</h4>
        <p>
          If the strategies do not meet expectations, returns may be lower than predicted or even
          negative.
        </p>
        <h4>Liquidity Risk:</h4>
        <p>
          If the strategy funds are not recovered in time when the Vault matures, redemption may be
          delayed.
        </p>
        <h4>Cross-chain Bridge Risk:</h4>
        <p>
          If the Vault involves cross-chain bridge operations, users bear the potential technical or
          delay risks during the bridging process.
        </p>
      </article>
    </div>
  );
}

function NumberPoint({ children }: { children: React.ReactNode }) {
  return (
    <div className="bl-text-white bl-bg-primary bl-rounded-full bl-flex bl-items-center bl-justify-center bl-text-xs bl-size-7 bl-font-title">
      {children}
    </div>
  );
}
