import { Client, DataWrapper } from '@/lib/api/client';
import { ActionFunctionArgs } from '@remix-run/cloudflare';
import { getSession } from '@/modules/session';

import {
  BtcFiTaskResponse,
  DailyResponse,
  DailyTaskResponse,
  LotteryResponse,
  InviteCodeResponse,
  InviteInfoResponse,
  InviteListResponse,
  HelmetResponse,
  RecheckResponse,
  BtcfiV2HeadlineResponse,
  BtcfiV2VaultsResponse,
  BtcfiV2UserProjectRewardResponse,
  BtcfiV2Project,
  BtcfiV2APYHistoryResponse,
  BtcfiV2APYHistoryItem,
} from './types';

class UserCenterAPI {
  protected client: Client;
  protected baseRoute: string = import.meta.env.VITE_USER_CENTER_BASE_ROUTE;

  constructor(client: Client) {
    this.client = client;
  }

  protected buildRoute(route: string) {
    return `${this.baseRoute}${route}`;
  }

  async dailyCheck(): Promise<DataWrapper<DailyResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/try_btcfi_daily_checkin'));
  }

  async dailyCancel(): Promise<DataWrapper<DailyResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/cancel_btcfi_daily_checkin'));
  }

  async recheck(): Promise<DataWrapper<DailyResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/try_btcfi_daily_recheckin'));
  }

  async recheckCancel(): Promise<DataWrapper<DailyResponse>> {
    return this.client.request('post', this.buildRoute('/v1/task/cancel_btcfi_daily_recheckin'));
  }

  async getTaskList(): Promise<DataWrapper<BtcFiTaskResponse>> {
    const resp = await this.client.request<BtcFiTaskResponse>(
      'get',
      this.buildRoute('/v1/task/btcfi_tasklist'),
    );
    if (resp.data) {
      const { btcfiTasks } = resp.data;
      btcfiTasks.sort((a, b) => a.rank - b.rank);

      resp.data.btcfiTasks = btcfiTasks;
    }
    return resp;
  }

  async getDailyTaskList(): Promise<DataWrapper<DailyTaskResponse>> {
    const resp = await this.client.request<DailyTaskResponse>(
      'get',
      this.buildRoute('/v1/task/btcfi_daily_checkin_task'),
    );
    return resp;
  }

  async getRecheckInfo(): Promise<DataWrapper<RecheckResponse>> {
    const resp = await this.client.request<RecheckResponse>(
      'get',
      this.buildRoute('/v1/task/btcfi_daily_reCheckin_task'),
    );
    return resp;
  }

  async getLotteryWinner(timestamp: number): Promise<DataWrapper<LotteryResponse>> {
    const params = new URLSearchParams({
      timestamp: timestamp.toString(),
    });
    return this.client.request(
      'get',
      this.buildRoute('/v1/task/btcfi_daily_lottery_winners'),
      params,
    );
  }

  async getUserBtr(): Promise<DataWrapper<{ btr: number }>> {
    return this.client.request('get', this.buildRoute('/v1/task/user_btr'));
  }

  async getOrderId(
    task_id?: string,
    from_page?: string,
  ): Promise<DataWrapper<{ orderId: string }>> {
    const params = {
      task_id: task_id || 0,
      from_page,
    };
    return this.client.request('post', this.buildRoute('/v1/task/btcfi_pre_claim'), params);
  }

  async getInviteCode(): Promise<DataWrapper<InviteCodeResponse>> {
    return this.client.request('get', this.buildRoute('/v1/task/btcfi_invite_code'));
  }

  async getHelmetReward(): Promise<DataWrapper<HelmetResponse>> {
    return this.client.request('get', this.buildRoute('/v1/task/btcfi_helmet_data'));
  }

  async getApplyInvite(invite_code: string) {
    const params = {
      invite_code,
    };
    return this.client.request('post', this.buildRoute('/v1/task/btcfi_signed'), params);
  }

  async getInviterInfo(): Promise<DataWrapper<InviteInfoResponse>> {
    return this.client.request('get', this.buildRoute('/v1/task/btcfi_inviter_info'));
  }

  async getIsOver(): Promise<DataWrapper<{ isOver: boolean }>> {
    return this.client.request('get', this.buildRoute('/v1/task/is_btcfi_over'));
  }

  async getInviteeList({
    page,
    page_size,
  }: {
    page: string;
    page_size: string;
  }): Promise<DataWrapper<InviteListResponse>> {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: page_size.toString(),
    });
    return this.client.request('get', this.buildRoute('/v1/task/btcfi_invitee_list'), params);
  }

  async calimLottery(params: { task_id: string }) {
    const result = await this.client.request<string>(
      'post',
      this.buildRoute('/v1/task/try_on_chain_task'),
      params,
    );
    return result;
  }

  async cancelLottery(params: { task_id: string }) {
    const result = await this.client.request<string>(
      'post',
      this.buildRoute('/v1/task/cancel_on_chain_task'),
      params,
    );
    return result;
  }

  async addUserop(params: {
    op_type: string;
    from_page: string;
    from_wallet: string;
    from_invite_code?: string;
  }) {
    const result = await this.client.request<string>(
      'post',
      this.buildRoute('/v1/task/add_user_op'),
      params,
    );
    return result;
  }

  async claimBinance() {
    const result = await this.client.request<string>(
      'post',
      this.buildRoute('/v1/task/reward/claim'),
      { wallet: 'binance' },
    );
    return result;
  }

  // New v2 btcfi endpoints
  async getBtcfiV2Headline(): Promise<BtcfiV2HeadlineResponse> {
    const resp = await this.client.request<BtcfiV2HeadlineResponse>(
      'get',
      this.buildRoute('/v2/btcfi/headline'),
    );
    return resp.data;
  }

  async getBtcfiV2Vaults(): Promise<BtcfiV2VaultsResponse> {
    const resp = await this.client.request<BtcfiV2VaultsResponse>(
      'get',
      this.buildRoute('/v2/btcfi/vaults'),
    );
    return resp.data;
  }

  async getBtcfiV2ProjectInfo(id: number): Promise<BtcfiV2Project> {
    const resp = await this.client.request<BtcfiV2Project>(
      'get',
      this.buildRoute(`/v2/btcfi/project/${id}`),
    );
    return resp.data;
  }

  async getBtcfiV2ProjectAPYHistory(id: number): Promise<BtcfiV2APYHistoryItem[]> {
    const resp = await this.client.request<BtcfiV2APYHistoryResponse>(
      'get',
      this.buildRoute(`/v2/btcfi/project/${id}/apy_history`),
    );
    return resp.data.list;
  }

  async getBtcfiV2UserProjectReward(projectId: number): Promise<BtcfiV2UserProjectRewardResponse> {
    const params = new URLSearchParams({
      projectId: projectId.toString(),
    });
    const resp = await this.client.request<BtcfiV2UserProjectRewardResponse>(
      'get',
      this.buildRoute('/v2/btcfi/user_reward'),
      params,
    );
    return resp.data;
  }
}

export const createAPIClient = (token?: string) => {
  const endpoint = import.meta.env.VITE_USER_CENTER_ENDPOINT;
  const client = new Client(endpoint);
  if (token) {
    client.setToken(token);
  }
  return new UserCenterAPI(client);
};

export const createAPIRquest = async (request: ActionFunctionArgs['request']) => {
  const session = await getSession(request);
  const token = session.get('user.token') as string | undefined;

  const cookieHeader = request.headers.get('Cookie');

  const cookies = cookieHeader
    ? Object.fromEntries(
        cookieHeader?.split(';').map((cookie) => {
          const [key, value] = cookie.trim().split('=');
          return [key, decodeURIComponent(value)];
        }),
      )
    : {};
  const visitorId = cookies['user.visitorId'] || null;
  const cfConnectingIp = request.headers.get('CF-Connecting-IP');
  const ip = cfConnectingIp || '';
  const endpoint = import.meta.env.VITE_USER_CENTER_ENDPOINT;
  const client = new Client(endpoint);
  if (token) {
    client.setToken(token);
  }
  if (ip) {
    client.setIp(ip);
  }
  if (visitorId) {
    client.setVisitorId(visitorId);
  }
  const api = new UserCenterAPI(client);
  return api;
};
