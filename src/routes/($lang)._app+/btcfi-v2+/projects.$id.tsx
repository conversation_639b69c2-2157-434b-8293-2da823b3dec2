import AVAXIcon from '@/components/icons/coins/AVAXIcon';
import SUIIcon from '@/components/icons/coins/SUIIcon';
import { Card, CardContent, CutCornerCard } from '@/components/ui/card';
import { ProjectCard } from '@/modules/btcfi-v2/components/project';
import { Link, useLoaderData } from '@remix-run/react';
import { ChevronsLeftIcon } from 'lucide-react';
import { DepositForm } from '@/modules/btcfi-v2/components/deposit';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RedeemForm } from '@/modules/btcfi-v2/components/redeem';
import { APYChart } from '@/modules/btcfi-v2/components/apy-chart';
import { BtcfiV2CustodyToken, BtcfiV2Project } from '@/modules/btcfi/types';
import { ThirdPartyContent } from '@/modules/btcfi-v2/components/third-party';
import { j<PERSON>, LoaderFunctionArgs } from '@remix-run/cloudflare';
import { createAPIClient } from '@/modules/btcfi/api.server';
import { toPercent } from '@/lib/utils';
import { Token } from '@/wallets/config/type';
import { chainMap } from '@/wallets/config/chains';

export async function loader({ params }: LoaderFunctionArgs) {
  const { id } = params;
  const api = createAPIClient();

  const project = await api.getBtcfiV2ProjectInfo(Number(id));
  const apyHistory = await api.getBtcfiV2ProjectAPYHistory(Number(id));

  return json({ project, apyHistory });
}

export default function BTCFiProjectPage() {
  const { project, apyHistory } = useLoaderData<typeof loader>();

  const projects = [
    {
      id: 1,
      name: 'SUI',
      icon: SUIIcon,
      description: 'SUI',
      apy: 1.12,
      chain: 'sui_mainnet',
    },
    {
      id: 2,
      name: 'Avalanche',
      icon: AVAXIcon,
      description: 'Avalanche',
      apy: 19.7,
      chain: 'avalanche_mainnet',
      tag: 'btr',
    },
  ];

  return (
    <section className="bl-container bl-space-y-6 bl-mb-12 bl-pt-20">
      <div className="bl-w-full bl-pt-6 bl-h-[80px] lg:bl-h-[200px] lg:bl-pt-15 bl-relative">
        <Breadcrumb />
        <div className="bl-absolute bl-size-[378px] bl-bg-primary bl-rounded-full bl-opacity-80 bl-blur-[256px] bl-top-[-240px] bl-left-[80px]"></div>
      </div>

      <div className="bl-flex bl-flex-col lg:bl-flex-row bl-gap-6">
        <Card className="bl-grow bl-bg-transparent bl-text-secondary">
          <CardContent className="bl-p-3 lg:bl-p-9">
            <APYChart data={apyHistory} />
            <div className="bl-border-b bl-border-card-border bl-py-8 bl-grid bl-grid-cols-2">
              <div className="bl-space-y-2">
                <div className="bl-text-sm lg:bl-text-base">TVL</div>
                <div className="bl-text-white bl-text-xl bl-font-bold lg:bl-text-[22px]">
                  ${Number(project.tvl).toLocaleString('en-US')}
                </div>
              </div>
              <div className="bl-space-y-2">
                <div className="bl-text-sm lg:bl-text-base">Est.APY</div>
                <div className="bl-text-white bl-text-xl bl-font-bold lg:bl-text-[22px]">
                  {toPercent(project.apy)}
                </div>
              </div>
            </div>
            <div
              className="bl-border-b bl-border-card-border bl-py-8 btcfi-v2-article"
              dangerouslySetInnerHTML={{
                __html: project.description,
              }}
            ></div>
            <div className="bl-py-8">
              <h3 className="bl-text-sm lg:bl-text-base">YBTC.B Earn</h3>
              <div className="bl-w-full bl-grid bl-gap-3.5 bl-mt-8 lg:bl-grid-cols-2 lg:bl-gap-7">
                {projects.map((project) => (
                  <ProjectCard key={project.id} project={project} className="bl-w-full" />
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <CutCornerCard
          containerClassName="bl-w-full bl-h-fit bl-order-first lg:bl-w-fit lg:bl-order-last"
          className="bl-w-full bl-py-10 bl-px-5 bl-bg-[#111] bl-space-y-8 lg:bl-w-[412px]"
        >
          <ActionCardContent project={project} />
        </CutCornerCard>
      </div>
    </section>
  );
}

function Breadcrumb() {
  return (
    <Link className="bl-flex bl-items-center bl-gap-2 hover:bl-underline" to="/">
      <ChevronsLeftIcon className="bl-size-6" />
      <div className="bl-flex bl-items-center bl-gap-1">
        <AVAXIcon className="bl-size-6" />
        <div>YBTC.B</div>
      </div>
    </Link>
  );
}

type ActionCardContentProps = {
  project: BtcfiV2Project;
};

function ActionCardContent({ project }: ActionCardContentProps) {
  const custody = project.custody;

  if (custody.self && custody.thirdParty) {
    const chain = chainMap[custody.self.staked.chain];
    const stakedToken = toToken(custody.self.staked);
    const receiptToken = toToken(custody.self.receipt);
    return (
      <Tabs defaultValue="self">
        <TabsList className="bl-w-full bl-grid bl-grid-cols-2 bl-h-9 bl-bg-secondary bl-p-0.5 bl-border-0">
          <TabsTrigger
            value="self"
            className="bl-w-full bl-text-base bl-font-bold bl-text-black data-[state=active]:bl-bg-black data-[state=active]:bl-text-white"
          >
            Bitlayer Custody
          </TabsTrigger>
          <TabsTrigger
            value="thirdParty"
            className="bl-w-full bl-text-base bl-font-bold bl-text-black data-[state=active]:bl-bg-black data-[state=active]:bl-text-white"
          >
            Non-custody
          </TabsTrigger>
        </TabsList>
        <TabsContent value="self" className="bl-mt-5">
          <Tabs defaultValue="deposit">
            <TabsList className="bl-w-full bl-grid bl-grid-cols-2 bl-h-9 bl-bg-transparent bl-p-0.5 bl-border-0">
              <TabsTrigger
                value="deposit"
                className="bl-w-full bl-text-lg bl-justify-start bl-font-medium bl-text-secondary/50 data-[state=active]:bl-bg-transparent data-[state=active]:bl-text-white"
              >
                Deposit
              </TabsTrigger>
              <TabsTrigger
                value="redeem"
                className="bl-w-full bl-text-lg bl-justify-start bl-font-medium bl-text-secondary/50 data-[state=active]:bl-bg-transparent data-[state=active]:bl-text-white"
              >
                Redeem
              </TabsTrigger>
            </TabsList>
            <TabsContent value="deposit" className="bl-mt-2">
              <DepositForm sourceToken={stakedToken} targetToken={receiptToken} chain={chain} />
            </TabsContent>
            <TabsContent value="redeem" className="bl-mt-2">
              <RedeemForm sourceToken={receiptToken} targetToken={stakedToken} chain={chain} />
            </TabsContent>
          </Tabs>
        </TabsContent>
        <TabsContent value="thirdParty" className="bl-mt-5">
          <ThirdPartyContent custody={custody.thirdParty} />
        </TabsContent>
      </Tabs>
    );
  } else if (custody.self && !custody.thirdParty) {
    const chain = chainMap[custody.self.staked.chain];
    const stakedToken = toToken(custody.self.staked);
    const receiptToken = toToken(custody.self.receipt);
    return (
      <Tabs defaultValue="deposit">
        <TabsList className="bl-w-full bl-grid bl-grid-cols-2 bl-h-9 bl-bg-secondary bl-p-0.5 bl-border-0">
          <TabsTrigger
            value="deposit"
            className="bl-w-full bl-text-base bl-font-bold bl-text-black data-[state=active]:bl-bg-black data-[state=active]:bl-text-white"
          >
            Deposit
          </TabsTrigger>
          <TabsTrigger
            value="redeem"
            className="bl-w-full bl-text-base bl-font-bold bl-text-black data-[state=active]:bl-bg-black data-[state=active]:bl-text-white"
          >
            Redeem
          </TabsTrigger>
        </TabsList>
        <TabsContent value="deposit" className="bl-mt-5">
          <DepositForm sourceToken={stakedToken} targetToken={receiptToken} chain={chain} />
        </TabsContent>
        <TabsContent value="redeem" className="bl-mt-5">
          <RedeemForm sourceToken={receiptToken} targetToken={stakedToken} chain={chain} />
        </TabsContent>
      </Tabs>
    );
  } else if (custody.thirdParty && !custody.self) {
    return <ThirdPartyContent custody={custody.thirdParty} />;
  }

  return null;
}

function toToken(token: BtcfiV2CustodyToken): Token {
  if (token.address === '0x0') {
    return {
      id: token.id,
      name: token.name,
      symbol: token.symbol,
      decimals: token.decimals,
      isNative: true,
      type: 'native',
      icon: token.icon,
    };
  }

  return {
    id: token.id,
    name: token.name,
    symbol: token.symbol,
    decimals: token.decimals,
    isNative: false,
    type: 'erc20',
    icon: token.icon,
    contractAddress: token.address,
  };
}
