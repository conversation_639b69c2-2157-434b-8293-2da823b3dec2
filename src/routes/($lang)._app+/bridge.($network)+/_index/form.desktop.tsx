import { useEffect, useMemo } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useAccount } from '@/hooks/wallet/account';
import { UseEnsureOnChain } from '@/hooks/wallet/chains';
import { BaseChainType, NetworkType } from '@/wallets/config/type';
import { ChevronDown, LoaderIcon } from 'lucide-react';
import { ChainIcon, WalletDrawer } from '@/components/featured/wallet';
import { Trans, useTranslation } from 'react-i18next';
import {
  EstimateTip,
  FormSection,
  SwapButton,
  ThirdPartyBridgeMenuItem,
  WalletConnector,
  chainName,
  bridgeI18nKey,
  MaintenanceTip,
} from '@/modules/bridge/components/common';
import { walletDrivers } from '@/hooks/wallet/common';
import {
  FeeText,
  TransferFormProps,
  formatTotalAmount,
  SelectTokenField,
  AmountSection,
} from '@/modules/bridge/components/form';
import { ControllerProps, FieldPath, FieldValues } from 'react-hook-form';
import { FormField } from '@/components/ui/form';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTransferHint, useValidate, useWorkflow } from '@/modules/bridge/hooks/transfer';
import { BigNumber } from 'ethers';

export const SelectChainField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  current,
  options,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, 'render'> & {
  name: 'left' | 'right';
  current?: BaseChainType;
  options: BaseChainType[];
}) => {
  const selectButtonClass =
    'md:bl-min-w-52 md:bl-w-full md:bl-h-10 md:bl-text-lg/none md:bl-px-4 bl-text-white bl-border-input';
  const menuItemClass = 'bl-text-base/5 bl-flex bl-gap-2 bl-items-center';

  return (
    <FormField
      {...props}
      render={({ field }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              type="button"
              className={selectButtonClass}
              outlineClassName="bl-border-input"
            >
              <div className="bl-flex bl-gap-2 bl-items-center bl-justify-between bl-w-full">
                <div className="bl-flex bl-gap-2 bl-items-center">
                  <ChainIcon icon={current?.icon} className="bl-size-5" />
                  {chainName({ chain: current, as: props.name })}
                </div>
                <ChevronDown className="bl-size-5 bl-text-primary bl-duration-200 group-data-[state=open]:bl-rotate-180" />
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            sideOffset={8}
            className="md:bl-min-w-52 md:bl-w-full bl-border-primary"
          >
            {options.map((chain) => {
              return (
                <DropdownMenuItem
                  key={chain.id}
                  className={menuItemClass}
                  onClick={() => field.onChange(chain.id)}
                >
                  <ChainIcon icon={chain.icon} className="bl-size-5" />
                  {chainName({ chain, as: props.name })}
                </DropdownMenuItem>
              );
            })}
            {!current?.testnet && options.some((c) => c.networkType === NetworkType.btc) && (
              <ThirdPartyBridgeMenuItem className={menuItemClass} />
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    />
  );
};

const ReceiverSection = ({
  fromChain,
  chain,
  senderAddress,
  network,
}: {
  fromChain?: BaseChainType;
  chain?: BaseChainType;
  senderAddress?: string;
  network?: NetworkType;
}) => {
  const { t } = useTranslation('', { keyPrefix: bridgeI18nKey });
  const { t: commontT } = useTranslation();

  const { address, driver: receiver } = useAccount({ network });
  const Icon = receiver ? walletDrivers[receiver]?.icon : null;

  const walletButtonClass = 'btn-xs bl-h-[26px] bl-w-full bl-text-sm';
  const bothEVM =
    fromChain?.networkType === NetworkType.evm && chain?.networkType === NetworkType.evm;

  const renderWalletButton = () => {
    if (address && receiver && chain) {
      return (
        <WalletDrawer address={address} chain={chain} shouldSwitch={!bothEVM}>
          <Button variant="secondary" size="xs" type="button" className={walletButtonClass}>
            <div className="bl-flex bl-gap-2 bl-items-center">
              {Icon && <Icon className="bl-size-4" />}
              {walletDrivers[receiver]?.name}
            </div>
          </Button>
        </WalletDrawer>
      );
    }

    if (senderAddress) {
      return (
        <WalletConnector chain={chain}>
          <Button
            variant="default"
            overlayVariant="outline"
            size="xs"
            type="button"
            className={cn(walletButtonClass, 'bl-text-white')}
          >
            <span>{commontT('common.connect')}</span>
          </Button>
        </WalletConnector>
      );
    }

    return null;
  };

  return (
    <FormSection className="bl-px-5 bl-py-3 md:bl-space-y-1">
      <div className="bl-flex bl-items-center bl-justify-between bl-text-base/none">
        <div>{t('recipientAddress')}</div>
        <EstimateTip network={network} fromChain={fromChain} toChain={chain} />
      </div>
      <div className="bl-flex bl-gap-6 bl-items-center bl-justify-between">
        <div
          className={cn('bl-text-white bl-text-sm bl-break-all', {
            'bl-text-secondary/60 bl-text-base/5': !address,
          })}
        >
          {address ?? t('recipientTip')}
        </div>
        <div className="bl-min-w-32 bl-shrink-0">{renderWalletButton()}</div>
      </div>
    </FormSection>
  );
};

export const DesktopTransferForm = ({
  form,
  leftOptions,
  rightOptions,
  fromChain,
  toChain,
  balance,
  tokens,
  state,
  fee,
  max,
  min,
  onSubmit,
  onSwap,
}: TransferFormProps) => {
  const { t } = useTranslation('', { keyPrefix: bridgeI18nKey });
  const { address: senderAddress, chainId: actualFromChainId } = useAccount({
    network: fromChain?.networkType,
  });
  const { address: receiverAddress, chainId: actualToChainId } = useAccount({
    network: toChain?.networkType,
  });
  const formBalance = form.watch('amount');
  const tokenId = form.watch('token');
  const address = form.watch('address');
  const token = useMemo(() => tokens.find((item) => item.id === tokenId)!, [tokenId, tokens]);
  const { setValue } = form;

  useEffect(() => {
    if (address !== receiverAddress) {
      setValue('address', receiverAddress);
    }
  }, [receiverAddress, setValue, address]);

  const { ensure } = UseEnsureOnChain();

  const workflow = useWorkflow({ from: senderAddress, fromChain, toChain, token });
  const [canTransfer, hint] = useValidate(workflow, {
    fromChain,
    formBalance,
    balance: balance?.value !== undefined ? BigNumber.from(balance.value) : undefined,
    token,
    fee,
    min,
    max,
    state,
    address: receiverAddress,
  });

  const transferHint = useTransferHint();

  const totalAmount = useMemo(() => {
    return formatTotalAmount(formBalance, fee, token);
  }, [formBalance, fee, token]);

  const transferButtonClass = 'bl-w-full bl-h-10';

  const renderTransferButton = () => {
    const text = transferHint ? t(transferHint) : hint;
    return (
      <Button
        variant="secondary"
        size="lg"
        className={transferButtonClass}
        type="submit"
        disabled={!canTransfer}
      >
        <div className="bl-flex bl-items-center bl-gap-2">
          {state === 'loading' && <LoaderIcon className="bl-size-6 bl-animate-spin" />}
          {text ? text : t('transfer')}
        </div>
      </Button>
    );
  };

  const renderSwitchButton = () => {
    const toBeEnsureChain = fromChain?.chain.id === actualFromChainId ? toChain : fromChain;
    return (
      <Button
        variant="outlineError"
        size="lg"
        overlayVariant="secondary"
        className={cn(transferButtonClass, 'hover:bl-border-primary')}
        outlineClassName="group-hover/button:bl-border-primary"
        type="button"
        onClick={() => ensure({ chain: toBeEnsureChain })}
      >
        <div>
          <Trans i18nKey="common.switchChain" values={{ chainName: toBeEnsureChain?.name }} />
        </div>
      </Button>
    );
  };

  const renderActionButton = () => {
    if (!senderAddress) {
      return (
        <WalletConnector chain={fromChain}>
          <Button
            variant="default"
            overlayVariant="outline"
            size="lg"
            className={cn(transferButtonClass, 'bl-text-white')}
            type="button"
          >
            <span>
              <Trans i18nKey="common.connect" />
            </span>
          </Button>
        </WalletConnector>
      );
    }

    // If the sender is not connected to the correct chain, show the switch button
    // If the receiver and sender are on the same chain, skip
    // If the receiver is not connected to the correct chain, show the switch button
    if (fromChain && fromChain.chain.id !== actualFromChainId) {
      return renderSwitchButton();
    } else if (toChain?.networkType === fromChain?.networkType) {
      // do nothing
    } else if (toChain && receiverAddress && toChain.chain.id !== actualToChainId) {
      return renderSwitchButton();
    }

    return renderTransferButton();
  };

  const FooterButton = () => {
    return (
      <div className="bl-w-full bl-pt-4.5">
        {renderActionButton()}
        <MaintenanceTip tokenId={tokenId} />
      </div>
    );
  };

  return (
    <form className="bl-space-y-3" onSubmit={onSubmit}>
      <div className="bl-w-full bl-flex bl-justify-between bl-items-end bl-pb-4.5">
        <div className="bl-space-y-2.5">
          <div className="bl-text-base/none">{t('from')}</div>
          <SelectChainField
            current={fromChain}
            control={form.control}
            name="left"
            options={leftOptions}
          />
        </div>
        <SwapButton onSwap={onSwap} fromChain={fromChain} chain={toChain} />
        <div className="bl-space-y-2.5">
          <div className="bl-text-base/none">{t('to')}</div>
          <SelectChainField
            current={toChain}
            control={form.control}
            name="right"
            options={rightOptions}
          />
        </div>
      </div>
      <AmountSection balance={balance} chain={fromChain} form={form} token={token}>
        <SelectTokenField
          form={form}
          name="token"
          control={form.control}
          current={token}
          fromChains={leftOptions}
          toChains={rightOptions}
        />
      </AmountSection>
      <ReceiverSection
        network={toChain?.networkType}
        fromChain={fromChain}
        chain={toChain}
        senderAddress={senderAddress}
      />
      <FormSection className="bl-px-4 bl-py-3">
        <div className="bl-grid bl-grid-cols-2">
          <div className="bl-flex bl-gap-1">
            <div className="bl-text-sm/4">{t('fee')}</div>
            <div className="bl-min-w-20 bl-text-white bl-text-base/4">
              <FeeText fee={fee} chain={fromChain} />
            </div>
          </div>
          <div className="bl-flex bl-gap-1">
            <div className="bl-text-sm/4">{t('total')}</div>
            <div className="bl-w-20 bl-text-white bl-text-base/4 bl-whitespace-nowrap">
              {formBalance && fee && token ? (
                <span className="bl-text-primary">{totalAmount}</span>
              ) : (
                '--'
              )}
            </div>
          </div>
        </div>
      </FormSection>
      <FooterButton />
    </form>
  );
};
